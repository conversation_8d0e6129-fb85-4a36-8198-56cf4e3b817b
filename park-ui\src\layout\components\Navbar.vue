<template>
  <div class="navbar">
    <hamburger id="hamburger-container" :is-active="appStore.sidebar.opened" class="hamburger-container" @toggleClick="toggleSideBar" />
    <breadcrumb v-if="!settingsStore.topNav" id="breadcrumb-container" class="breadcrumb-container" />
    <top-nav v-if="settingsStore.topNav" id="topmenu-container" class="topmenu-container" />

    <div class="right-menu">
      <template v-if="appStore.device !== 'mobile'">

        <screenfull id="screenfull" class="right-menu-item hover-effect" />

        <el-tooltip content="主题模式" effect="dark" placement="bottom">
          <div class="right-menu-item hover-effect theme-switch-wrapper" @click="toggleTheme">
            <svg-icon v-if="settingsStore.isDark" icon-class="sunny" />
            <svg-icon v-if="!settingsStore.isDark" icon-class="moon" />
          </div>
        </el-tooltip>

        <el-tooltip content="布局大小" effect="dark" placement="bottom">
          <size-select id="size-select" class="right-menu-item hover-effect" />
        </el-tooltip>
      </template>

      <el-dropdown @command="handleCommand" class="avatar-container right-menu-item hover-effect" trigger="hover">
        <div class="avatar-wrapper">
          <img :src="userStore.avatar" class="user-avatar" />
          <span class="user-nickname"> {{ userStore.nickName }} </span>
        </div>
        <template #dropdown>
          <el-dropdown-menu>
            <router-link to="/user/profile">
              <el-dropdown-item>个人中心</el-dropdown-item>
            </router-link>
            <el-dropdown-item divided command="logout">
              <span>退出登录</span>
            </el-dropdown-item>
          </el-dropdown-menu>
        </template>
      </el-dropdown>
      <div class="right-menu-item hover-effect setting" @click="setLayout" v-if="settingsStore.showSettings">
        <svg-icon icon-class="more-up" />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ElMessageBox } from 'element-plus'
import Breadcrumb from '@/components/Breadcrumb'
import TopNav from '@/components/TopNav'
import Hamburger from '@/components/Hamburger'
import Screenfull from '@/components/Screenfull'
import SizeSelect from '@/components/SizeSelect'
import useAppStore from '@/store/modules/app'
import useUserStore from '@/store/modules/user'
import useSettingsStore from '@/store/modules/settings'

const appStore = useAppStore()
const userStore = useUserStore()
const settingsStore = useSettingsStore()

function toggleSideBar() {
  appStore.toggleSideBar()
}

function handleCommand(command) {
  switch (command) {
    case "setLayout":
      setLayout()
      break
    case "logout":
      logout()
      break
    default:
      break
  }
}

function logout() {
  ElMessageBox.confirm('确定注销并退出系统吗？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(() => {
    userStore.logOut().then(() => {
      location.href = '/index'
    })
  }).catch(() => { })
}

const emits = defineEmits(['setLayout'])
function setLayout() {
  emits('setLayout')
}

function toggleTheme() {
  settingsStore.toggleTheme()
}
</script>

<style lang='scss' scoped>
.navbar {
  height: 50px;
  overflow: hidden;
  position: relative;
  background: var(--navbar-bg);
  box-shadow: 0 1px 4px rgba(0, 21, 41, 0.08);

  .hamburger-container {
    line-height: 46px;
    height: 100%;
    float: left;
    cursor: pointer;
    transition: background 0.3s;
    -webkit-tap-highlight-color: transparent;

    &:hover {
      background: rgba(0, 0, 0, 0.025);
    }
  }

  .breadcrumb-container {
    float: left;
  }

  .topmenu-container {
    position: absolute;
    left: 50px;
  }

  .errLog-container {
    display: inline-block;
    vertical-align: top;
  }

  .right-menu {
    float: right;
    height: 100%;
    line-height: 50px;
    display: flex;

    &:focus {
      outline: none;
    }

    .right-menu-item {
      display: inline-block;
      padding: 0 8px;
      height: 100%;
      font-size: 18px;
      color: #5a5e66;
      vertical-align: text-bottom;

      &.hover-effect {
        cursor: pointer;
        transition: background 0.3s;

        &:hover {
          background: rgba(0, 0, 0, 0.025);
        }
      }

      &.theme-switch-wrapper {
        display: flex;
        align-items: center;

        svg {
          transition: transform 0.3s;
          
          &:hover {
            transform: scale(1.15);
          }
        }
      }
    }

    .avatar-container {
      margin-right: 0px;
      padding-right: 0px;

      .avatar-wrapper {
        margin-top: 10px;
        right: 5px;
        position: relative;

        .user-avatar {
          cursor: pointer;
          width: 30px;
          height: 30px;
          border-radius: 50%;
        }

        .user-nickname{
          position: relative;
          left: 5px;
          bottom: 10px;
          font-size: 14px;
          font-weight: bold;
        }

        i {
          cursor: pointer;
          position: absolute;
          right: -20px;
          top: 25px;
          font-size: 12px;
        }
      }
    }
  }
}

/* 星空主题导航栏样式 */
html.dark .navbar {
  background: rgba(15, 20, 25, 0.95) !important;
  backdrop-filter: blur(15px);
  border-bottom: 1px solid rgba(100, 200, 255, 0.2) !important;
  box-shadow:
    0 2px 8px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(100, 200, 255, 0.1);
  position: relative;

  // 添加微妙的星空效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(1px 1px at 30px 20px, rgba(255,255,255,0.2), transparent),
      radial-gradient(1px 1px at 80px 35px, rgba(100,200,255,0.15), transparent);
    background-repeat: repeat;
    background-size: 120px 50px;
    animation: starry-twinkle 8s ease-in-out infinite alternate;
    pointer-events: none;
    z-index: 1;
  }

  .hamburger-container {
    position: relative;
    z-index: 2;

    &:hover {
      background: rgba(100, 200, 255, 0.1) !important;
    }

    .hamburger {
      color: #e3f2fd !important;
      filter: drop-shadow(0 0 6px rgba(227, 242, 253, 0.6));
      transition: all 0.3s ease;

      &:hover {
        color: #ffffff !important;
        filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.8));
        transform: scale(1.1);
      }
    }
  }

  .breadcrumb-container {
    position: relative;
    z-index: 2;

    .el-breadcrumb {
      .el-breadcrumb__item {
        .el-breadcrumb__inner {
          color: #b3e5fc !important;
          text-shadow: 0 0 5px rgba(179, 229, 252, 0.4);
          transition: all 0.3s ease;

          &:hover {
            color: #e3f2fd !important;
            text-shadow: 0 0 8px rgba(227, 242, 253, 0.6);
          }
        }

        .el-breadcrumb__separator {
          color: #81d4fa !important;
        }
      }
    }
  }

  .right-menu {
    position: relative;
    z-index: 2;

    .right-menu-item {
      color: #e3f2fd !important;
      transition: all 0.3s ease;

      &:hover {
        background: rgba(100, 200, 255, 0.1) !important;
        color: #ffffff !important;
        transform: scale(1.05);
      }

      .el-icon {
        filter: drop-shadow(0 0 6px rgba(227, 242, 253, 0.6));

        &:hover {
          filter: drop-shadow(0 0 10px rgba(255, 255, 255, 0.8));
        }
      }
    }

    .theme-switch-wrapper {
      .el-icon {
        color: #90caf9 !important;
        filter: drop-shadow(0 0 8px rgba(144, 202, 249, 0.6));
        transition: all 0.3s ease;

        &:hover {
          color: #e3f2fd !important;
          filter: drop-shadow(0 0 12px rgba(227, 242, 253, 0.8));
          transform: scale(1.2) rotate(15deg);
        }
      }
    }

    .avatar-container {
      .avatar-wrapper {
        color: #e3f2fd !important;
        text-shadow: 0 0 5px rgba(227, 242, 253, 0.4);

        &:hover {
          color: #ffffff !important;
          text-shadow: 0 0 8px rgba(255, 255, 255, 0.6);
        }

        .user-avatar {
          border: 2px solid rgba(100, 200, 255, 0.3);
          box-shadow: 0 0 10px rgba(100, 200, 255, 0.3);
          transition: all 0.3s ease;

          &:hover {
            border-color: rgba(100, 200, 255, 0.6);
            box-shadow: 0 0 15px rgba(100, 200, 255, 0.5);
            transform: scale(1.1);
          }
        }
      }
    }
  }
}
</style>
