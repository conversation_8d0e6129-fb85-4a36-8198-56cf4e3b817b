// 星空主题样式文件
// ===========================

// 星空动画关键帧定义
@keyframes starry-twinkle {
  0%, 100% { 
    opacity: 0.3; 
    transform: scale(1); 
  }
  50% { 
    opacity: 1; 
    transform: scale(1.2); 
  }
}

@keyframes shooting-star {
  0% { 
    transform: translateX(-100px) translateY(100px);
    opacity: 0;
  }
  10% { opacity: 1; }
  90% { opacity: 1; }
  100% { 
    transform: translateX(100vw) translateY(-100px);
    opacity: 0;
  }
}

@keyframes aurora-glow {
  0%, 100% { 
    opacity: 0.2; 
    transform: translateX(-50%) scaleX(1);
  }
  50% { 
    opacity: 0.6; 
    transform: translateX(-50%) scaleX(1.2);
  }
}

@keyframes starry-pulse {
  0%, 100% { 
    box-shadow: 0 0 5px rgba(100, 200, 255, 0.3);
  }
  50% { 
    box-shadow: 0 0 20px rgba(100, 200, 255, 0.6), 0 0 30px rgba(100, 200, 255, 0.4);
  }
}

// 星空背景混合器
@mixin starry-background {
  background: 
    radial-gradient(ellipse at top, #1e3c72 0%, #2a5298 50%, #0f1419 100%),
    radial-gradient(ellipse at bottom, #0f1419 0%, #1a237e 50%, #000051 100%);
  background-attachment: fixed;
  position: relative;
  
  // 星星层
  &::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image: 
      radial-gradient(2px 2px at 20px 30px, #eee, transparent),
      radial-gradient(2px 2px at 40px 70px, rgba(255,255,255,0.8), transparent),
      radial-gradient(1px 1px at 90px 40px, #fff, transparent),
      radial-gradient(1px 1px at 130px 80px, rgba(255,255,255,0.6), transparent),
      radial-gradient(2px 2px at 160px 30px, #ddd, transparent),
      radial-gradient(1px 1px at 200px 50px, rgba(255,255,255,0.7), transparent),
      radial-gradient(2px 2px at 250px 90px, #fff, transparent),
      radial-gradient(1px 1px at 300px 20px, rgba(255,255,255,0.5), transparent);
    background-repeat: repeat;
    background-size: 350px 200px;
    animation: starry-twinkle 4s ease-in-out infinite alternate;
    pointer-events: none;
    z-index: 1;
  }
  
  // 极光层
  &::after {
    content: '';
    position: fixed;
    top: 0;
    left: 50%;
    width: 200%;
    height: 100%;
    background: linear-gradient(45deg, 
      rgba(64, 224, 208, 0.1) 0%, 
      transparent 25%, 
      transparent 50%,
      rgba(138, 43, 226, 0.1) 75%,
      transparent 100%);
    animation: aurora-glow 8s ease-in-out infinite alternate;
    pointer-events: none;
    z-index: 2;
    transform: translateX(-50%);
  }
}

// 流星效果混合器
@mixin shooting-star-effect {
  &::after {
    content: '';
    position: fixed;
    top: 10%;
    left: -5%;
    width: 2px;
    height: 2px;
    background: #fff;
    border-radius: 50%;
    box-shadow: 0 0 6px #fff, 0 0 12px #fff, 0 0 18px #fff;
    animation: shooting-star 6s linear infinite;
    animation-delay: 2s;
    pointer-events: none;
    z-index: 3;
  }
}

// 星空卡片样式混合器
@mixin starry-card {
  background: rgba(255, 255, 255, 0.05) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(100, 200, 255, 0.2) !important;
  border-radius: 12px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.3),
    inset 0 1px 0 rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
  
  // 光晕效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, 
      transparent, 
      rgba(100, 200, 255, 0.1), 
      transparent);
    transition: left 0.5s ease;
  }
  
  &:hover {
    border-color: rgba(100, 200, 255, 0.4) !important;
    box-shadow: 
      0 8px 32px rgba(0, 0, 0, 0.3),
      0 0 20px rgba(100, 200, 255, 0.2),
      inset 0 1px 0 rgba(255, 255, 255, 0.2);
    transform: translateY(-2px);
    
    &::before {
      left: 100%;
    }
  }
}

// 星空按钮样式混合器
@mixin starry-button {
  background: linear-gradient(135deg, 
    rgba(100, 200, 255, 0.2) 0%, 
    rgba(138, 43, 226, 0.2) 100%) !important;
  border: 1px solid rgba(100, 200, 255, 0.3) !important;
  color: #e3f2fd !important;
  border-radius: 8px;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  
  &::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, 
      rgba(255, 255, 255, 0.3) 0%, 
      transparent 70%);
    transition: all 0.3s ease;
    transform: translate(-50%, -50%);
    pointer-events: none;
  }
  
  &:hover {
    background: linear-gradient(135deg, 
      rgba(100, 200, 255, 0.3) 0%, 
      rgba(138, 43, 226, 0.3) 100%) !important;
    border-color: rgba(100, 200, 255, 0.5) !important;
    box-shadow: 
      0 0 20px rgba(100, 200, 255, 0.3),
      inset 0 0 20px rgba(255, 255, 255, 0.1);
    transform: translateY(-2px);
    color: #ffffff !important;
    
    &::before {
      width: 100px;
      height: 100px;
    }
  }
  
  &:focus {
    background: linear-gradient(135deg, 
      rgba(100, 200, 255, 0.3) 0%, 
      rgba(138, 43, 226, 0.3) 100%) !important;
    border-color: rgba(100, 200, 255, 0.5) !important;
    box-shadow: 
      0 0 20px rgba(100, 200, 255, 0.3),
      inset 0 0 20px rgba(255, 255, 255, 0.1);
    color: #ffffff !important;
  }
}

// 星空文字样式混合器
@mixin starry-text($size: 'normal') {
  @if $size == 'large' {
    color: #e3f2fd !important;
    text-shadow: 0 0 10px rgba(100, 200, 255, 0.5);
    font-weight: 600;
  } @else if $size == 'small' {
    color: #b3e5fc !important;
    text-shadow: 0 0 5px rgba(100, 200, 255, 0.3);
  } @else {
    color: #e1f5fe !important;
    text-shadow: 0 0 8px rgba(100, 200, 255, 0.4);
  }
}

// 星空图标样式混合器
@mixin starry-icon($color: #64b5f6) {
  color: $color !important;
  filter: drop-shadow(0 0 8px rgba(red($color), green($color), blue($color), 0.6));
  transition: all 0.3s ease;
  
  &:hover {
    filter: drop-shadow(0 0 12px rgba(red($color), green($color), blue($color), 0.8));
    transform: scale(1.1);
  }
}

// 星空输入框样式混合器
@mixin starry-input {
  background: rgba(255, 255, 255, 0.05) !important;
  border: 1px solid rgba(100, 200, 255, 0.2) !important;
  color: #e1f5fe !important;
  border-radius: 6px;
  
  &:focus {
    border-color: rgba(100, 200, 255, 0.5) !important;
    box-shadow: 0 0 10px rgba(100, 200, 255, 0.3) !important;
    background: rgba(255, 255, 255, 0.08) !important;
  }
  
  &::placeholder {
    color: #81d4fa !important;
  }
}

// 星空下拉菜单样式混合器
@mixin starry-dropdown {
  background: rgba(15, 20, 25, 0.95) !important;
  backdrop-filter: blur(20px);
  border: 1px solid rgba(100, 200, 255, 0.2) !important;
  border-radius: 8px;
  box-shadow:
    0 20px 60px rgba(0, 0, 0, 0.5),
    0 0 40px rgba(100, 200, 255, 0.1);
}

// 星空弹窗样式混合器
@mixin starry-dialog {
  .el-dialog {
    background: rgba(15, 20, 25, 0.95) !important;
    backdrop-filter: blur(20px);
    border: 1px solid rgba(100, 200, 255, 0.2) !important;
    border-radius: 16px;
    box-shadow:
      0 20px 60px rgba(0, 0, 0, 0.5),
      0 0 40px rgba(100, 200, 255, 0.1);

    .el-dialog__header {
      background: linear-gradient(135deg,
        rgba(100, 200, 255, 0.1) 0%,
        rgba(138, 43, 226, 0.1) 100%) !important;
      border-bottom: 1px solid rgba(100, 200, 255, 0.2) !important;
      border-radius: 16px 16px 0 0;

      .el-dialog__title {
        @include starry-text('large');
      }
    }

    .el-dialog__body {
      background: rgba(15, 20, 25, 0.8) !important;
      @include starry-text('normal');
    }

    .el-dialog__footer {
      background: rgba(15, 20, 25, 0.9) !important;
      border-top: 1px solid rgba(100, 200, 255, 0.2) !important;
      border-radius: 0 0 16px 16px;
    }
  }

  .el-overlay {
    background: rgba(0, 0, 0, 0.7) !important;
    backdrop-filter: blur(5px);
  }
}

// 星空表格样式混合器
@mixin starry-table {
  .el-table {
    background: rgba(15, 20, 25, 0.8) !important;
    border: 1px solid rgba(100, 200, 255, 0.2) !important;
    border-radius: 12px;
    overflow: hidden;

    .el-table__header {
      background: linear-gradient(135deg,
        rgba(100, 200, 255, 0.1) 0%,
        rgba(138, 43, 226, 0.1) 100%) !important;

      th {
        background: transparent !important;
        border-bottom: 1px solid rgba(100, 200, 255, 0.2) !important;
        @include starry-text('normal');
      }
    }

    .el-table__body {
      tr {
        background: rgba(15, 20, 25, 0.6) !important;

        &:hover {
          background: rgba(100, 200, 255, 0.1) !important;
        }

        td {
          border-bottom: 1px solid rgba(100, 200, 255, 0.1) !important;
          @include starry-text('small');
        }
      }
    }
  }
}

// 星空侧边栏样式混合器
@mixin starry-sidebar {
  background: rgba(15, 20, 25, 0.9) !important;
  backdrop-filter: blur(10px);
  border-right: 1px solid rgba(100, 200, 255, 0.2) !important;

  .el-menu {
    background: transparent !important;
    border: none !important;

    .el-menu-item {
      background: transparent !important;
      @include starry-text('normal');
      transition: all 0.3s ease;

      &:hover {
        background: rgba(100, 200, 255, 0.1) !important;
        @include starry-text('large');
      }

      &.is-active {
        background: rgba(100, 200, 255, 0.2) !important;
        @include starry-text('large');
        border-right: 3px solid #64b5f6;
      }
    }

    .el-sub-menu {
      .el-sub-menu__title {
        background: transparent !important;
        @include starry-text('normal');

        &:hover {
          background: rgba(100, 200, 255, 0.1) !important;
          @include starry-text('large');
        }
      }
    }
  }
}

// 星空导航栏样式混合器
@mixin starry-navbar {
  background: rgba(15, 20, 25, 0.95) !important;
  backdrop-filter: blur(10px);
  border-bottom: 1px solid rgba(100, 200, 255, 0.2) !important;

  .navbar-content {
    @include starry-text('normal');
  }
}

// ===========================
// 星空主题全局应用
// ===========================

// 在html.dark下应用星空主题
html.dark {
  // 全局背景
  @include starry-background;
  min-height: 100vh;

  // 确保body也有星空背景
  body {
    @include starry-background;
    min-height: 100vh;
  }

  // 应用程序容器
  #app {
    @include starry-background;
    min-height: 100vh;
    position: relative;
    z-index: 10;
  }

  // Element Plus 组件覆盖
  .el-card {
    @include starry-card;
  }

  .el-button {
    @include starry-button;
  }

  .el-input__wrapper {
    @include starry-input;
  }

  .el-select .el-input__wrapper {
    @include starry-input;
  }

  .el-textarea__inner {
    @include starry-input;
  }

  // 弹窗组件
  @include starry-dialog;

  // 表格组件
  @include starry-table;

  // 下拉菜单
  .el-dropdown-menu {
    @include starry-dropdown;

    .el-dropdown-menu__item {
      @include starry-text('normal');

      &:hover {
        background: rgba(100, 200, 255, 0.1) !important;
        @include starry-text('large');
      }
    }
  }

  // 消息提示
  .el-message {
    background: rgba(15, 20, 25, 0.95) !important;
    border: 1px solid rgba(100, 200, 255, 0.2) !important;
    backdrop-filter: blur(10px);
    @include starry-text('normal');
  }

  .el-notification {
    background: rgba(15, 20, 25, 0.95) !important;
    border: 1px solid rgba(100, 200, 255, 0.2) !important;
    backdrop-filter: blur(10px);

    .el-notification__title {
      @include starry-text('large');
    }

    .el-notification__content {
      @include starry-text('normal');
    }
  }

  // 分页组件
  .el-pagination {
    .el-pagination__total,
    .el-pagination__jump {
      @include starry-text('normal');
    }

    .el-pager li {
      background: rgba(255, 255, 255, 0.05) !important;
      border: 1px solid rgba(100, 200, 255, 0.2) !important;
      @include starry-text('normal');

      &:hover {
        background: rgba(100, 200, 255, 0.1) !important;
      }

      &.is-active {
        background: rgba(100, 200, 255, 0.2) !important;
        border-color: rgba(100, 200, 255, 0.5) !important;
        @include starry-text('large');
      }
    }
  }

  // 标签页
  .el-tabs {
    .el-tabs__header {
      background: rgba(15, 20, 25, 0.8) !important;
      border-bottom: 1px solid rgba(100, 200, 255, 0.2) !important;
    }

    .el-tabs__item {
      @include starry-text('normal');

      &:hover {
        @include starry-text('large');
      }

      &.is-active {
        @include starry-text('large');
        border-bottom-color: #64b5f6 !important;
      }
    }
  }

  // 面包屑
  .el-breadcrumb {
    .el-breadcrumb__item {
      .el-breadcrumb__inner {
        @include starry-text('normal');

        &:hover {
          @include starry-text('large');
        }
      }
    }
  }
}
