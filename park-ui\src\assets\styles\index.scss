@import './variables.module.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './ruoyi.scss';
@import './starry-theme.scss';

body {
  height: 100%;
  margin: 0;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
}

label {
  font-weight: 700;
}

html {
  height: 100%;
  box-sizing: border-box;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

.no-padding {
  padding: 0px !important;
}

.padding-content {
  padding: 4px 0;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.pr-5 {
  padding-right: 5px;
}

.pl-5 {
  padding-left: 5px;
}

.block {
  display: block;
}

.pointer {
  cursor: pointer;
}

.inlineBlock {
  display: block;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

//main-container全局样式
.app-container {
  padding: 20px;
}

.components-container {
  margin: 30px 50px;
  position: relative;
}

.text-center {
  text-align: center
}

.sub-navbar {
  height: 50px;
  line-height: 50px;
  position: relative;
  width: 100%;
  text-align: right;
  padding-right: 20px;
  transition: 600ms ease position;
  background: linear-gradient(90deg, rgba(32, 182, 249, 1) 0%, rgba(32, 182, 249, 1) 0%, rgba(33, 120, 241, 1) 100%, rgba(33, 120, 241, 1) 100%);

  .subtitle {
    font-size: 20px;
    color: #fff;
  }

  &.draft {
    background: #d0d0d0;
  }

  &.delete_flag {
    background: #d0d0d0;
  }
}

.link-type,
.link-type:focus {
  color: #337ab7;
  cursor: pointer;

  &:hover {
    color: rgb(32, 160, 255);
  }
}

.filter-container {
  padding-bottom: 10px;

  .filter-item {
    display: inline-block;
    vertical-align: middle;
    margin-bottom: 10px;
  }
}

// 修复Element Plus表格固定列透明度问题
.el-table {
  .el-table__fixed-right {
    background-color: #ffffff !important;

    .el-table__fixed-body-wrapper {
      background-color: #ffffff !important;
    }

    .el-table__cell {
      background-color: #ffffff !important;
    }
  }

  .el-table__fixed-right-patch {
    background-color: #ffffff !important;
  }

  // 确保固定列的单元格背景不透明
  .el-table__body-wrapper .el-table__row .el-table__cell.is-right {
    background-color: #ffffff !important;
  }

  // 修复固定列头部透明度
  .el-table__fixed-header-wrapper {
    background-color: #ffffff !important;

    .el-table__cell {
      background-color: #ffffff !important;
    }
  }

  // 针对特定class-name的固定列修复
  .el-table__cell.small-padding.fixed-width {
    background-color: #ffffff !important;
  }

  // 更全面的固定列背景修复
  .el-table__fixed-right .el-table__cell.small-padding,
  .el-table__fixed-right .el-table__cell.fixed-width,
  .el-table__fixed-right .el-table__cell.small-padding.fixed-width {
    background-color: #ffffff !important;
  }

  // 确保所有右侧固定列都有白色背景
  .el-table__body-wrapper .el-table__row .el-table__cell[class*="fixed-width"] {
    background-color: #ffffff !important;
  }

  // 修复表格行悬停时固定列的背景
  .el-table__body-wrapper .el-table__row:hover .el-table__cell.is-right,
  .el-table__body-wrapper .el-table__row:hover .el-table__cell[class*="fixed-width"] {
    background-color: #f5f7fa !important;
  }
}
