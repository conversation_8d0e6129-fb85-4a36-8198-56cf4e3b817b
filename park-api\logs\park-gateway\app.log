2025-08-01 02:40:05.629  WARN 26864 --- [Thread-12] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-08-01 02:40:05.629  WARN 26864 --- [Thread-6] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-01 02:40:05.646  WARN 26864 --- [Thread-12] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-08-01 02:40:05.648  WARN 26864 --- [Thread-6] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-08-01 02:40:05.691  INFO 26864 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
2025-08-01 02:40:05.712  INFO 26864 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
2025-08-01 09:18:08.050  INFO 6160 --- [main] com.lgjy.gateway.LgjyGatewayApplication  : The following 1 profile is active: "dev"
2025-08-01 09:18:08.931  INFO 6160 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 09:18:08.934  INFO 6160 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 09:18:08.958  INFO 6160 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 8 ms. Found 0 Redis repository interfaces.
2025-08-01 09:18:09.146  INFO 6160 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=a28cdf3d-2497-3ef5-9b94-3ee1e1ff5151
2025-08-01 09:18:09.354  INFO 6160 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 09:18:09.355  INFO 6160 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 09:18:09.356  INFO 6160 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 09:18:15.652  INFO 6160 --- [main] c.a.c.s.g.s.SentinelSCGAutoConfiguration : [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-08-01 09:18:15.714  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [After]
2025-08-01 09:18:15.714  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Before]
2025-08-01 09:18:15.714  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Between]
2025-08-01 09:18:15.714  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Cookie]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Header]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Host]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Method]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Path]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Query]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Weight]
2025-08-01 09:18:15.715  INFO 6160 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-08-01 09:18:16.063  INFO 6160 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-01 09:18:16.127  INFO 6160 --- [main] c.a.c.s.g.s.SentinelSCGAutoConfiguration : [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-08-01 09:18:16.375  WARN 6160 --- [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-08-01 09:18:16.540  INFO 6160 --- [main] o.s.b.web.embedded.netty.NettyWebServer  : Netty started on port 8080
2025-08-01 09:18:18.821  INFO 6160 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 09:18:18.821  INFO 6160 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 09:18:19.012  INFO 6160 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP park-gateway 192.168.3.140:8080 register finished
2025-08-01 09:18:19.140  INFO 6160 --- [main] a.c.n.d.NacosDiscoveryHeartBeatPublisher : Start nacos heartBeat task scheduler.
2025-08-01 09:18:19.165  INFO 6160 --- [main] com.lgjy.gateway.LgjyGatewayApplication  : Started LgjyGatewayApplication in 17.082 seconds (JVM running for 18.882)
2025-08-01 09:18:19.172  INFO 6160 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-gateway, group=DEFAULT_GROUP
2025-08-01 09:18:19.174  INFO 6160 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-gateway-dev.yml, group=DEFAULT_GROUP
2025-08-01 09:18:19.174  INFO 6160 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-gateway.yml, group=DEFAULT_GROUP
2025-08-01 09:18:19.176  INFO 6160 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=common-dev.yml, group=DEFAULT_GROUP
2025-08-01 10:18:28.005  WARN 6160 --- [Thread-12] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-08-01 10:18:28.005  WARN 6160 --- [Thread-6] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-01 10:18:28.016  WARN 6160 --- [Thread-12] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-08-01 10:18:28.023  WARN 6160 --- [Thread-6] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-08-01 10:18:28.073  INFO 6160 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
2025-08-01 10:18:28.091  INFO 6160 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
2025-08-01 10:18:41.772  INFO 38616 --- [main] com.lgjy.gateway.LgjyGatewayApplication  : The following 1 profile is active: "dev"
2025-08-01 10:18:42.589  INFO 38616 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Multiple Spring Data modules found, entering strict repository configuration mode
2025-08-01 10:18:42.591  INFO 38616 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Bootstrapping Spring Data Redis repositories in DEFAULT mode.
2025-08-01 10:18:42.616  INFO 38616 --- [main] .s.d.r.c.RepositoryConfigurationDelegate : Finished Spring Data repository scanning in 9 ms. Found 0 Redis repository interfaces.
2025-08-01 10:18:42.793  INFO 38616 --- [main] o.s.cloud.context.scope.GenericScope     : BeanFactory id=a28cdf3d-2497-3ef5-9b94-3ee1e1ff5151
2025-08-01 10:18:43.018  INFO 38616 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 10:18:43.020  INFO 38616 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig' of type [org.springframework.cloud.client.loadbalancer.reactive.LoadBalancerBeanPostProcessorAutoConfiguration$ReactorDeferringLoadBalancerFilterConfig] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 10:18:43.021  INFO 38616 --- [main] trationDelegate$BeanPostProcessorChecker : Bean 'reactorDeferringLoadBalancerExchangeFilterFunction' of type [org.springframework.cloud.client.loadbalancer.reactive.DeferringLoadBalancerExchangeFilterFunction] is not eligible for getting processed by all BeanPostProcessors (for example: not eligible for auto-proxying)
2025-08-01 10:18:49.473  INFO 38616 --- [main] c.a.c.s.g.s.SentinelSCGAutoConfiguration : [Sentinel SpringCloudGateway] register SentinelGatewayFilter with order: -2147483648
2025-08-01 10:18:49.543  INFO 38616 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [After]
2025-08-01 10:18:49.543  INFO 38616 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Before]
2025-08-01 10:18:49.543  INFO 38616 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Between]
2025-08-01 10:18:49.543  INFO 38616 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Cookie]
2025-08-01 10:18:49.543  INFO 38616 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Header]
2025-08-01 10:18:49.543  INFO 38616 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Host]
2025-08-01 10:18:49.543  INFO 38616 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Method]
2025-08-01 10:18:49.543  INFO 38616 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Path]
2025-08-01 10:18:49.543  INFO 38616 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Query]
2025-08-01 10:18:49.543  INFO 38616 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [ReadBody]
2025-08-01 10:18:49.543  INFO 38616 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [RemoteAddr]
2025-08-01 10:18:49.543  INFO 38616 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [XForwardedRemoteAddr]
2025-08-01 10:18:49.543  INFO 38616 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [Weight]
2025-08-01 10:18:49.543  INFO 38616 --- [main] o.s.c.g.r.RouteDefinitionRouteLocator    : Loaded RoutePredicateFactory [CloudFoundryRouteService]
2025-08-01 10:18:49.932  INFO 38616 --- [main] o.s.b.a.e.web.EndpointLinksResolver      : Exposing 1 endpoint(s) beneath base path '/actuator'
2025-08-01 10:18:49.990  INFO 38616 --- [main] c.a.c.s.g.s.SentinelSCGAutoConfiguration : [Sentinel SpringCloudGateway] register SentinelGatewayBlockExceptionHandler
2025-08-01 10:18:50.221  WARN 38616 --- [main] iguration$LoadBalancerCaffeineWarnLogger : Spring Cloud LoadBalancer is currently working with the default cache. While this cache implementation is useful for development and tests, it's recommended to use Caffeine cache in production.You can switch to using Caffeine cache, by adding it and org.springframework.cache.caffeine.CaffeineCacheManager to the classpath.
2025-08-01 10:18:50.397  INFO 38616 --- [main] o.s.b.web.embedded.netty.NettyWebServer  : Netty started on port 8080
2025-08-01 10:18:52.760  INFO 38616 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.impl.NacosClientAuthServiceImpl success.
2025-08-01 10:18:52.760  INFO 38616 --- [main] c.a.n.p.a.s.c.ClientAuthPluginManager    : [ClientAuthPluginManager] Load ClientAuthService com.alibaba.nacos.client.auth.ram.RamClientAuthServiceImpl success.
2025-08-01 10:18:52.943  INFO 38616 --- [main] c.a.c.n.registry.NacosServiceRegistry    : nacos registry, DEFAULT_GROUP park-gateway 192.168.3.140:8080 register finished
2025-08-01 10:18:53.077  INFO 38616 --- [main] a.c.n.d.NacosDiscoveryHeartBeatPublisher : Start nacos heartBeat task scheduler.
2025-08-01 10:18:53.103  INFO 38616 --- [main] com.lgjy.gateway.LgjyGatewayApplication  : Started LgjyGatewayApplication in 18.248 seconds (JVM running for 20.116)
2025-08-01 10:18:53.113  INFO 38616 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-gateway, group=DEFAULT_GROUP
2025-08-01 10:18:53.114  INFO 38616 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-gateway-dev.yml, group=DEFAULT_GROUP
2025-08-01 10:18:53.115  INFO 38616 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=park-gateway.yml, group=DEFAULT_GROUP
2025-08-01 10:18:53.116  INFO 38616 --- [main] c.a.c.n.refresh.NacosContextRefresher    : [Nacos Config] Listening config: dataId=common-dev.yml, group=DEFAULT_GROUP
2025-08-01 12:35:35.989 ERROR 38616 --- [reactor-http-nio-15] c.l.g.handler.GatewayExceptionHandler    : [网关异常处理]请求路径:/system/dict/data/type/sys_user_sex,异常信息:Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
2025-08-01 12:35:35.989 ERROR 38616 --- [reactor-http-nio-2] c.l.g.handler.GatewayExceptionHandler    : [网关异常处理]请求路径:/system/user/list,异常信息:Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
2025-08-01 12:35:35.989 ERROR 38616 --- [reactor-http-nio-1] c.l.g.handler.GatewayExceptionHandler    : [网关异常处理]请求路径:/system/user/deptTree,异常信息:Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
2025-08-01 12:35:35.989 ERROR 38616 --- [reactor-http-nio-16] c.l.g.handler.GatewayExceptionHandler    : [网关异常处理]请求路径:/system/dict/data/type/sys_normal_disable,异常信息:Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
2025-08-01 12:35:46.318 ERROR 38616 --- [reactor-http-nio-3] c.l.g.handler.GatewayExceptionHandler    : [网关异常处理]请求路径:/system/config/configKey/sys.user.initPassword,异常信息:Redis command timed out; nested exception is io.lettuce.core.RedisCommandTimeoutException: Command timed out after 10 second(s)
2025-08-01 12:36:01.446 ERROR 38616 --- [reactor-http-nio-8] com.lgjy.gateway.filter.AuthFilter       : [鉴权异常处理]请求路径:/system/menu/getRouters,错误信息:登录状态已过期
2025-08-01 13:09:10.092 ERROR 38616 --- [reactor-http-nio-5] c.l.g.handler.GatewayExceptionHandler    : [网关异常处理]请求路径:/system/user/getInfo,异常信息:Connection timed out: no further information: /192.168.3.140:9201
2025-08-01 13:09:19.967 ERROR 38616 --- [reactor-http-nio-7] c.l.g.handler.GatewayExceptionHandler    : [网关异常处理]请求路径:/auth/logout,异常信息:Connection timed out: no further information: /192.168.3.140:9204
2025-08-01 13:09:29.580  WARN 38616 --- [reactor-http-nio-8] reactor.netty.channel.FluxReceive        : [8c61c699-1, L:/0:0:0:0:0:0:0:1:8080 - R:/0:0:0:0:0:0:0:1:14021] An exception has been observed post termination, use DEBUG level to see the full stack: java.io.IOException: 远程主机强迫关闭了一个现有的连接。
2025-08-01 13:10:03.915 ERROR 38616 --- [reactor-http-nio-10] c.l.g.handler.GatewayExceptionHandler    : [网关异常处理]请求路径:/system/user/getInfo,异常信息:Connection timed out: no further information: /192.168.3.140:9201
2025-08-01 13:10:13.903 ERROR 38616 --- [reactor-http-nio-12] c.l.g.handler.GatewayExceptionHandler    : [网关异常处理]请求路径:/auth/logout,异常信息:Connection timed out: no further information: /192.168.3.140:9204
2025-08-01 13:10:24.177 ERROR 38616 --- [reactor-http-nio-14] c.l.g.handler.GatewayExceptionHandler    : [网关异常处理]请求路径:/system/user/getInfo,异常信息:Connection timed out: no further information: /192.168.3.140:9201
2025-08-01 13:10:25.705 ERROR 38616 --- [reactor-http-nio-15] c.l.g.handler.GatewayExceptionHandler    : [网关异常处理]请求路径:/system/user/getInfo,异常信息:Connection timed out: no further information: /192.168.3.140:9201
2025-08-01 13:10:35.698 ERROR 38616 --- [reactor-http-nio-16] c.l.g.handler.GatewayExceptionHandler    : [网关异常处理]请求路径:/system/user/getInfo,异常信息:Connection timed out: no further information: /192.168.3.140:9201
2025-08-01 13:10:35.699 ERROR 38616 --- [reactor-http-nio-1] c.l.g.handler.GatewayExceptionHandler    : [网关异常处理]请求路径:/auth/logout,异常信息:Connection timed out: no further information: /192.168.3.140:9204
2025-08-01 13:10:36.193 ERROR 38616 --- [reactor-http-nio-2] c.l.g.handler.GatewayExceptionHandler    : [网关异常处理]请求路径:/auth/logout,异常信息:Connection timed out: no further information: /192.168.3.140:9204
2025-08-01 13:10:45.702 ERROR 38616 --- [reactor-http-nio-3] c.l.g.handler.GatewayExceptionHandler    : [网关异常处理]请求路径:/system/user/getInfo,异常信息:Connection timed out: no further information: /192.168.3.140:9201
2025-08-01 13:10:55.708 ERROR 38616 --- [reactor-http-nio-5] c.l.g.handler.GatewayExceptionHandler    : [网关异常处理]请求路径:/auth/logout,异常信息:Connection timed out: no further information: /192.168.3.140:9204
2025-08-01 13:16:19.649  WARN 38616 --- [Thread-9] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Start destroying common HttpClient
2025-08-01 13:16:19.649  WARN 38616 --- [Thread-13] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Start destroying Publisher
2025-08-01 13:16:19.650  WARN 38616 --- [Thread-13] c.a.nacos.common.notify.NotifyCenter     : [NotifyCenter] Destruction of the end
2025-08-01 13:16:19.653  WARN 38616 --- [Thread-9] c.a.n.common.http.HttpClientBeanHolder   : [HttpClientBeanHolder] Destruction of the end
2025-08-01 13:16:19.710  INFO 38616 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registering from Nacos Server now...
2025-08-01 13:16:19.748  INFO 38616 --- [SpringApplicationShutdownHook] c.a.c.n.registry.NacosServiceRegistry    : De-registration finished.
