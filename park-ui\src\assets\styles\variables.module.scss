// base color
$blue: #324157;
$light-blue: #333c46;
$red: #C03639;
$pink: #E65D6E;
$green: #30B08F;
$tiffany: #4AB7BD;
$yellow: #FEC171;
$panGreen: #30B08F;

// 默认主题变量
$menuText: #bfcbd9;
$menuActiveText: #409eff;
$menuBg: #2c5aa0;
$menuHover: #263445;

// 浅色主题theme-light
$menuLightBg: #ffffff;
$menuLightHover: #f0f1f5;
$menuLightText: #303133;
$menuLightActiveText: #409EFF;

// 基础变量
$base-sidebar-width: 200px;
$sideBarWidth: 200px;

// 菜单暗色变量
$base-menu-color: #bfcbd9;
$base-menu-color-active: #f4f4f5;
$base-menu-background: #304156;
$base-sub-menu-background: #1f2d3d;
$base-sub-menu-hover: #001528;

// 组件变量
$--color-primary: #409EFF;
$--color-success: #67C23A;
$--color-warning: #E6A23C;
$--color-danger: #F56C6C;
$--color-info: #909399;

:export {
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  menuBg: $menuBg;
  menuHover: $menuHover;
  menuLightBg: $menuLightBg;
  menuLightHover: $menuLightHover;
  menuLightText: $menuLightText;
  menuLightActiveText: $menuLightActiveText;
  sideBarWidth: $sideBarWidth;
  // 导出基础颜色
  blue: $blue;
  lightBlue: $light-blue;
  red: $red;
  pink: $pink;
  green: $green;
  tiffany: $tiffany;
  yellow: $yellow;
  panGreen: $panGreen;
  // 导出组件颜色
  colorPrimary: $--color-primary;
  colorSuccess: $--color-success;
  colorWarning: $--color-warning;
  colorDanger: $--color-danger;
  colorInfo: $--color-info;
}

// CSS变量定义
:root {
  /* 亮色模式变量 */
  --sidebar-bg: #{$menuBg};
  --sidebar-text: #{$menuText};
  --menu-hover: #{$menuHover};

  --navbar-bg: #ffffff;
  --navbar-text: #303133;

  /* splitpanes default-theme 变量 */
  --splitpanes-default-bg: #ffffff;

}

// 暗黑模式变量 - 星空主题
html.dark {
  /* 默认通用 - 星空背景 */
  --el-bg-color: rgba(15, 20, 25, 0.8);
  --el-bg-color-page: radial-gradient(ellipse at top, #1e3c72 0%, #2a5298 50%, #0f1419 100%);
  --el-bg-color-overlay: rgba(15, 20, 25, 0.95);
  --el-text-color-primary: #e3f2fd;
  --el-text-color-regular: #b3e5fc;
  --el-border-color: rgba(100, 200, 255, 0.2);
  --el-border-color-light: rgba(100, 200, 255, 0.1);

  /* 侧边栏 - 星空样式 */
  --sidebar-bg: rgba(15, 20, 25, 0.9);
  --sidebar-text: #e1f5fe;
  --menu-hover: rgba(100, 200, 255, 0.1);
  --menu-active-text: #64b5f6;

  /* 顶部导航栏 - 星空样式 */
  --navbar-bg: rgba(15, 20, 25, 0.95);
  --navbar-text: #e3f2fd;
  --navbar-hover: rgba(100, 200, 255, 0.1);

  /* 标签栏 - 星空样式 */
  --tags-bg: rgba(15, 20, 25, 0.9);
  --tags-item-bg: rgba(255, 255, 255, 0.05);
  --tags-item-border: rgba(100, 200, 255, 0.2);
  --tags-item-text: #b3e5fc;
  --tags-item-hover: rgba(100, 200, 255, 0.1);
  --tags-close-hover: rgba(100, 200, 255, 0.3);

  /* splitpanes 组件星空模式变量 */
  --splitpanes-bg: rgba(15, 20, 25, 0.8);
  --splitpanes-border: rgba(100, 200, 255, 0.2);
  --splitpanes-splitter-bg: rgba(255, 255, 255, 0.05);
  --splitpanes-splitter-hover-bg: rgba(100, 200, 255, 0.1);

  /* blockquote 星空模式变量 */
  --blockquote-bg: rgba(255, 255, 255, 0.05);
  --blockquote-border: rgba(100, 200, 255, 0.2);
  --blockquote-text: #b3e5fc;

  /* Cron 时间表达式星空模式变量 */
  --cron-border: rgba(100, 200, 255, 0.2);

  /* splitpanes default-theme 星空模式变量 */
  --splitpanes-default-bg: rgba(15, 20, 25, 0.8);

  /* 星空主题专用变量 */
  --starry-primary: #64b5f6;
  --starry-secondary: #90caf9;
  --starry-accent: #8a2be2;
  --starry-glow: rgba(100, 200, 255, 0.3);
  --starry-card-bg: rgba(255, 255, 255, 0.05);
  --starry-card-border: rgba(100, 200, 255, 0.1);
  --starry-button-bg: linear-gradient(135deg, rgba(100, 200, 255, 0.2) 0%, rgba(138, 43, 226, 0.2) 100%);
  --starry-button-border: rgba(100, 200, 255, 0.3);
  --starry-input-bg: rgba(255, 255, 255, 0.05);
  --starry-input-border: rgba(100, 200, 255, 0.2);

  /* 侧边栏菜单覆盖 */
  .sidebar-container {

    .el-menu-item,
    .menu-title {
      color: var(--el-text-color-regular);
    }

    & .theme-dark .nest-menu .el-sub-menu>.el-sub-menu__title,
    & .theme-dark .el-sub-menu .el-menu-item {
      background-color: var(--el-bg-color) !important;
    }
  }

  /* 顶部栏栏菜单覆盖 */
  .el-menu--horizontal {
    .el-menu-item {
      &:not(.is-disabled) {

        &:hover,
        &:focus {
          background-color: var(--navbar-hover) !important;
        }
      }
    }
  }

  /* 分割窗格覆盖 */
  .splitpanes {
    background-color: var(--splitpanes-bg);

    .splitpanes__pane {
      background-color: var(--splitpanes-bg);
      border-color: var(--splitpanes-border);
    }

    .splitpanes__splitter {
      background-color: var(--splitpanes-splitter-bg);
      border-color: var(--splitpanes-border);

      &:hover {
        background-color: var(--splitpanes-splitter-hover-bg);
      }

      &:before,
      &:after {
        background-color: var(--splitpanes-border);
      }
    }
  }

  /* 表格样式覆盖 */
  .el-table {
    --el-table-header-bg-color: var(--el-bg-color-overlay) !important;
    --el-table-header-text-color: var(--el-text-color-regular) !important;
    --el-table-border-color: var(--el-border-color-light) !important;
    --el-table-row-hover-bg-color: var(--el-bg-color-overlay) !important;

    .el-table__header-wrapper,
    .el-table__fixed-header-wrapper {
      th {
        background-color: var(--el-bg-color-overlay, #f8f8f9) !important;
        color: var(--el-text-color-regular, #515a6e);
      }
    }
  }

  /* 树组件高亮样式覆盖 */
  .el-tree {
    .el-tree-node.is-current>.el-tree-node__content {
      background-color: var(--el-bg-color-overlay) !important;
      color: var(--el-color-primary);
    }

    .el-tree-node__content:hover {
      background-color: var(--el-bg-color-overlay);
    }
  }

  /* 下拉菜单样式覆盖 */
  .el-dropdown-menu__item:not(.is-disabled):focus,
  .el-dropdown-menu__item:not(.is-disabled):hover {
    background-color: var(--navbar-hover) !important;
  }

  /* blockquote样式覆盖 */
  blockquote {
    background-color: var(--blockquote-bg) !important;
    border-left-color: var(--blockquote-border) !important;
    color: var(--blockquote-text) !important;
  }

  /* 时间表达式标题样式覆盖 */
  .popup-result .title {
    background: var(--cron-border);
  }

}