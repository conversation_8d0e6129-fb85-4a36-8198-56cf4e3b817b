<template>
  <div class="dashboard-container">
    <!-- 顶部主要统计数据 -->
    <div class="stats-grid">
      <!-- 总场库数量 -->
      <el-card class="stat-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon parking-lot">
            <el-icon>
              <OfficeBuilding />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-label">总场库数量</div>
            <div class="stat-value">{{ statsData.totalParkingLots }}</div>
            <div class="stat-change positive">
              <el-icon>
                <ArrowUp />
              </el-icon>
              <span>{{ statsData.parkingLotsGrowth }}%</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 总车位数量 -->
      <el-card class="stat-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon parking-space">
            <el-icon>
              <Grid />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-label">总车位数量</div>
            <div class="stat-value">{{ statsData.totalParkingSpaces }}</div>
            <div class="stat-change positive">
              <el-icon>
                <ArrowUp />
              </el-icon>
              <span>{{ statsData.parkingSpacesGrowth }}%</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 会员总数量 -->
      <el-card class="stat-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon members">
            <el-icon>
              <User />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-label">会员总数量</div>
            <div class="stat-value">{{ statsData.totalMembers }}</div>
            <div class="stat-change positive">
              <el-icon>
                <ArrowUp />
              </el-icon>
              <span>{{ statsData.membersGrowth }}%</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 小程序用户总数 -->
      <el-card class="stat-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon app-users">
            <el-icon>
              <UserFilled />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-label">小程序用户总数</div>
            <div class="stat-value">{{ statsData.totalAppUsers }}</div>
            <div class="stat-change positive">
              <el-icon>
                <ArrowUp />
              </el-icon>
              <span>{{ statsData.appUsersGrowth }}%</span>
            </div>
          </div>
        </div>
      </el-card>

      <!-- 总收入 -->
      <el-card class="stat-card revenue-card" shadow="hover">
        <div class="stat-content">
          <div class="stat-icon revenue">
            <el-icon>
              <Money />
            </el-icon>
          </div>
          <div class="stat-info">
            <div class="stat-label">{{ periodLabels[selectedPeriod] }}总收入</div>
            <div class="stat-value">¥{{ formatNumber(statsData.totalRevenue) }}</div>
            <div class="stat-change positive">
              <el-icon>
                <ArrowUp />
              </el-icon>
              <span>{{ statsData.revenueGrowth }}%</span>
            </div>
          </div>
        </div>
      </el-card>
    </div>

    <!-- 控制面板 -->
    <el-card class="control-panel" shadow="never">
      <div class="controls-container">
        <!-- 左侧：场库选择 -->
        <div class="warehouse-selector control-item">
          <span class="selector-label">选择场库：</span>
          <el-cascader
            v-model="selectedWarehouse"
            :options="warehouseCascaderOptions"
            :props="{
              value: 'value',
              label: 'label',
              children: 'children',
              emitPath: false,
              checkStrictly: true,
              expandTrigger: 'hover'
            }"
            placeholder="查看特定场库数据"
            style="width: 220px"
            clearable
            filterable
            :show-all-levels="false"
            @change="handleWarehouseChange"
          />
        </div>

        <!-- 中间：场库详情 -->
        <div class="warehouse-detail-wrapper control-item" v-if="selectedWarehouse">
          <div class="warehouse-title">{{ getWarehouseName(selectedWarehouse) }} 数据详情</div>
          <div class="warehouse-stats">
            <div class="warehouse-stat-item">
              <span class="label">车位数量:</span>
              <span class="value">{{ warehouseStats.parkingSpaces }}</span>
            </div>
            <div class="warehouse-stat-item">
              <span class="label">会员数量:</span>
              <span class="value">{{ warehouseStats.members }}</span>
            </div>
            <div class="warehouse-stat-item">
              <span class="label">今日收入:</span>
              <span class="value">¥{{ warehouseStats.todayRevenue }}</span>
            </div>
          </div>
        </div>

        <!-- 右侧：时间选择 -->
        <div class="time-selector control-item">
          <el-radio-group v-model="selectedPeriod" @change="handlePeriodChange">
            <el-radio-button value="week">近7天</el-radio-button>
            <el-radio-button value="month">近一月</el-radio-button>
            <el-radio-button value="halfYear">近半年</el-radio-button>
            <el-radio-button value="year">近一年</el-radio-button>
          </el-radio-group>
        </div>
      </div>
    </el-card>

    <!-- 图表区域 -->
    <div class="charts-section">
      <el-card class="chart-container" shadow="never">
        <template #header>
          <div class="chart-header">
            <span class="chart-title">收入趋势</span>
            <div class="chart-controls">
              <span class="control-label">Y轴刻度：</span>
              <el-button-group class="y-axis-controls">
                <el-button
                  v-for="scale in revenueYAxisScales"
                  :key="scale.value"
                  :type="Number(selectedRevenueYScale) === Number(scale.value) ? 'primary' : 'default'"
                  size="small"
                  @click="changeRevenueYAxisScale(scale.value)"
                >
                  {{scale.label}}
                </el-button>
              </el-button-group>
            </div>
          </div>
        </template>
        <div class="chart-content">
          <div ref="revenueChartRef" class="chart" style="height: 350px;"></div>
        </div>
      </el-card>

      <el-card class="chart-container" shadow="never">
        <template #header>
          <div class="chart-header">
            <span class="chart-title">用户增长趋势</span>
            <div class="chart-controls">
              <span class="control-label">Y轴刻度：</span>
              <el-button-group class="y-axis-controls">
                <el-button
                  v-for="scale in yAxisScales"
                  :key="scale"
                  :type="selectedYScale === scale ? 'primary' : 'default'"
                  size="small"
                  @click="changeYAxisScale(scale)"
                >
                  {{scale}}人
                </el-button>
              </el-button-group>
            </div>
          </div>
        </template>
        <div class="chart-content">
          <div ref="usersChartRef" class="chart" style="height: 350px;"></div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, onUnmounted, nextTick, watch } from 'vue'
import * as echarts from 'echarts'
import {
  OfficeBuilding,
  Grid,
  User,
  Money,
  UserFilled,
  ArrowUp
} from '@element-plus/icons-vue'
import {
  getDashboardStatistics,
  getSystemStatistics,
  getRevenueTrend,
  getUserTrend,
  getWarehouseList,
  getWarehouseOptions,
  getWarehouseStats
} from '@/api/dashboard'
import { ElMessage } from 'element-plus'

// Responsive data
const selectedPeriod = ref('week')
const selectedWarehouse = ref('')
const revenueChartRef = ref(null)
const usersChartRef = ref(null)

// Y轴刻度控制
const yAxisScales = [20, 50, 100]
const selectedYScale = ref(20)

// 收入图表Y轴刻度控制
const revenueYAxisScales = [
  { value: 1000, label: '千' },
  { value: 10000, label: '万' },
  { value: 100000, label: '十万' },
  { value: 1000000, label: '百万' }
]
const selectedRevenueYScale = ref(10000)

// Time period labels
const periodLabels = {
  week: '近7天',
  month: '近一月',
  halfYear: '近半年',
  year: '近一年'
}

// Statistics data
const statsData = reactive({
  totalParkingLots: 0,
  parkingLotsGrowth: 0,
  totalParkingSpaces: 0,
  parkingSpacesGrowth: 0,
  totalMembers: 0,
  membersGrowth: 0,
  totalRevenue: 0,
  revenueGrowth: 0,
  totalAppUsers: 0,
  appUsersGrowth: 0
})

// Warehouse list
const warehouseList = ref([])
const warehouseCascaderOptions = ref([])

// Loading states
const loading = ref(false)
const chartLoading = ref(false)

// Warehouse specific statistics
const warehouseStats = reactive({
  appUsers: 0,
  members: 0
})

// Chart instances
let revenueChart = null
let usersChart = null

// Load system statistics (top cards - always show system-wide data)
const loadSystemStatistics = async () => {
  try {
    loading.value = true
    const response = await getSystemStatistics(selectedPeriod.value)
    if (response.code === 200) {
      Object.assign(statsData, response.data)
    } else {
      ElMessage.error('获取系统统计数据失败：' + response.msg)
    }
  } catch (error) {
    console.error('获取系统统计数据失败:', error)
    ElMessage.error('获取系统统计数据失败，请稍后重试')
  } finally {
    loading.value = false
  }
}

// Load dashboard statistics (for charts - can be filtered by warehouse)
const loadDashboardStatistics = async () => {
  try {
    const response = await getDashboardStatistics(selectedPeriod.value, selectedWarehouse.value)
    if (response.code === 200) {
      // 这里可以处理图表相关的数据，如果需要的话
      console.log('Dashboard statistics loaded for charts:', response.data)
    } else {
      ElMessage.error('获取图表数据失败：' + response.msg)
    }
  } catch (error) {
    console.error('获取图表数据失败:', error)
    ElMessage.error('获取图表数据失败，请稍后重试')
  }
}

// Load warehouse list
const loadWarehouseList = async () => {
  try {
    const response = await getWarehouseOptions()
    if (response.code === 200) {
      warehouseList.value = response.data
      // 构建级联选择器选项
      warehouseCascaderOptions.value = buildWarehouseCascaderOptions(response.data)
    } else {
      ElMessage.error('获取场库列表失败：' + response.msg)
    }
  } catch (error) {
    console.error('获取场库列表失败:', error)
    ElMessage.error('获取场库列表失败，请稍后重试')
  }
}

// Load warehouse statistics
const loadWarehouseStatistics = async (warehouseId) => {
  if (!warehouseId) {
    Object.assign(warehouseStats, { appUsers: 0, members: 0 })
    return
  }

  try {
    const response = await getWarehouseStats(warehouseId)
    if (response.code === 200) {
      Object.assign(warehouseStats, response.data)
    } else {
      ElMessage.error('获取场库统计失败：' + response.msg)
    }
  } catch (error) {
    console.error('获取场库统计失败:', error)
    ElMessage.error('获取场库统计失败，请稍后重试')
  }
}

// Format numbers for display
const formatNumber = (num) => {
  if (num >= 10000) {
    return (num / 10000).toFixed(1) + '万'
  }
  return num.toLocaleString()
}

// Build warehouse cascader options
const buildWarehouseCascaderOptions = (warehouses) => {
  if (!warehouses || warehouses.length === 0) {
    return []
  }

  // 分离主场库和子场库 - 修复数据类型比较问题
  const mainWarehouses = warehouses.filter(w => w.parentId === 0 || w.parentId === "0")
  const subWarehouses = warehouses.filter(w => w.parentId !== 0 && w.parentId !== "0")

  // 构建级联结构
  return mainWarehouses.map(mainWarehouse => {
    const children = subWarehouses
      .filter(sub => sub.parentId == mainWarehouse.id) // 使用 == 而不是 === 来处理数字/字符串类型差异
      .map(sub => ({
        value: sub.id,
        label: sub.warehouseName,
        isLeaf: true
      }))

    return {
      value: mainWarehouse.id,
      label: mainWarehouse.warehouseName,
      children: children.length > 0 ? children : undefined
    }
  })
}

// Get warehouse name by ID
const getWarehouseName = (id) => {
  const warehouse = warehouseList.value.find(w => w.id === id)
  return warehouse ? warehouse.warehouseName : ''
}

// Initialize the revenue chart
const initRevenueChart = () => {
  if (!revenueChartRef.value) return
  revenueChart = echarts.init(revenueChartRef.value)
  updateRevenueChart()
}

// Update the revenue chart
const updateRevenueChart = async () => {
  if (!revenueChart) return

  try {
    chartLoading.value = true
    const response = await getRevenueTrend(selectedPeriod.value, selectedWarehouse.value)

    if (response.code === 200) {
      const { labels, data } = response.data

      // 处理数据为空或全为0的情况
      const hasData = data && data.length > 0 && data.some(val => val > 0)

      // 确保即使没有数据也有基本的标签和数据点
      const displayLabels = labels && labels.length > 0 ? labels : ['暂无数据']
      const displayData = hasData ? data : new Array(displayLabels.length).fill(0)

      const option = {
        title: {
          text: '收入趋势分析',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333'
          },
          left: 'center',
          top: 10
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(50, 50, 50, 0.95)',
          borderColor: '#409EFF',
          borderWidth: 2,
          borderRadius: 8,
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: params => {
            const value = params[0].value
            const date = params[0].axisValue
            return `
              <div style="padding: 8px;">
                <div style="margin-bottom: 6px; font-weight: bold;">${date}</div>
                <div style="display: flex; align-items: center;">
                  <span style="display: inline-block; width: 10px; height: 10px; background: #409EFF; border-radius: 50%; margin-right: 8px;"></span>
                  收入金额: <span style="color: #67C23A; font-weight: bold;">¥${value.toLocaleString()}</span>
                </div>
              </div>
            `
          }
        },
        legend: {
          data: ['收入趋势'],
          top: 40,
          textStyle: {
            color: '#666',
            fontSize: 12
          }
        },
        grid: {
          left: '8%',
          right: '8%',
          bottom: '20%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: displayLabels,
          axisLine: {
            lineStyle: {
              color: '#d0d0d0',
              width: 2
            }
          },
          axisLabel: {
            color: '#666',
            fontSize: 11,
            margin: 10,
            rotate: hasData && labels.length > 10 ? 45 : 0
          },
          axisTick: {
            alignWithLabel: true,
            lineStyle: {
              color: '#d0d0d0'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '收入金额 (元)',
          nameLocation: 'middle',
          nameGap: 50,
          nameTextStyle: {
            color: '#666',
            fontSize: 12,
            fontWeight: 'bold'
          },
          axisLine: {
            show: true, // 强制显示Y轴线
            lineStyle: {
              color: '#d0d0d0',
              width: 2
            }
          },
          axisLabel: {
            show: true, // 强制显示Y轴标签
            color: '#666',
            fontSize: 11,
            formatter: value => {
              if (value >= 10000) return `¥${(value / 10000).toFixed(1)}万`
              if (value >= 1000) return `¥${(value / 1000).toFixed(1)}k`
              return `¥${value}`
            }
          },
          splitLine: {
            show: true, // 强制显示分割线
            lineStyle: {
              color: '#f5f5f5',
              type: 'dashed'
            }
          },
          axisTick: {
            show: true // 强制显示Y轴刻度
          },
          min: 0,
          max: selectedRevenueYScale.value, // 动态Y轴最大值
          interval: selectedRevenueYScale.value / 5, // 动态间隔
          scale: false // 禁用自动缩放，保持固定范围
        },
        series: [{
          name: '收入趋势',
          type: 'line',
          smooth: true,
          symbol: 'circle',
          symbolSize: hasData ? 8 : 6,
          lineStyle: {
            color: hasData ? '#409EFF' : '#E6A23C',
            width: hasData ? 3 : 2,
            type: hasData ? 'solid' : 'dashed'
          },
          itemStyle: {
            color: hasData ? '#409EFF' : '#E6A23C',
            borderWidth: 2,
            borderColor: '#fff'
          },
          areaStyle: hasData ? {
            color: {
              type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
              colorStops: [
                { offset: 0, color: 'rgba(64, 158, 255, 0.4)' },
                { offset: 1, color: 'rgba(64, 158, 255, 0.1)' }
              ]
            }
          } : undefined,
          data: displayData
        }]
      }

      // 添加时间标识
      const timeLabel = generateTimeLabel(selectedPeriod.value)

      // 添加图表元素
      const graphicElements = [
        {
          type: 'text',
          right: 20,
          bottom: 20,
          style: {
            text: timeLabel,
            fontSize: 12,
            fill: '#666',
            textAlign: 'right'
          }
        }
      ]

      // 如果没有数据，添加空数据提示
      if (!hasData) {
        graphicElements.push({
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: '暂无收入数据\n请等待数据更新',
            fontSize: 16,
            fontWeight: 'bold',
            fill: '#999',
            textAlign: 'center'
          }
        })
      }

      option.graphic = graphicElements

      revenueChart.setOption(option)
    } else {
      ElMessage.error('获取收入趋势数据失败：' + response.msg)
    }
  } catch (error) {
    console.error('获取收入趋势数据失败:', error)
    ElMessage.error('获取收入趋势数据失败，请稍后重试')
  } finally {
    chartLoading.value = false
  }
}

// Initialize the users chart
const initUsersChart = () => {
  if (!usersChartRef.value) return
  usersChart = echarts.init(usersChartRef.value)
  updateUsersChart()
}

// Update the users chart
const updateUsersChart = async () => {
  if (!usersChart) return

  try {
    const response = await getUserTrend(selectedPeriod.value, selectedWarehouse.value)

    if (response.code === 200) {
      const { labels, membersData, appUsersData } = response.data

      // 处理数据为空的情况
      const hasMemberData = membersData && membersData.length > 0 && membersData.some(val => val > 0)
      const hasAppUserData = appUsersData && appUsersData.length > 0 && appUsersData.some(val => val > 0)
      const hasAnyData = hasMemberData || hasAppUserData

      const displayLabels = labels || []
      // 确保数据为整数
      const displayMembersData = hasMemberData ? membersData.map(val => Math.round(val || 0)) : new Array(labels.length).fill(0)
      const displayAppUsersData = hasAppUserData ? appUsersData.map(val => Math.round(val || 0)) : new Array(labels.length).fill(0)

      const option = {
        title: {
          text: '用户增长趋势',
          textStyle: {
            fontSize: 16,
            fontWeight: 'bold',
            color: '#333'
          },
          left: 'center',
          top: 10
        },
        tooltip: {
          trigger: 'axis',
          backgroundColor: 'rgba(50, 50, 50, 0.95)',
          borderColor: '#409EFF',
          borderWidth: 2,
          borderRadius: 8,
          textStyle: {
            color: '#fff',
            fontSize: 14
          },
          formatter: params => {
            const date = params[0].axisValue
            let content = `<div style="padding: 8px;"><div style="margin-bottom: 6px; font-weight: bold;">${date}</div>`

            params.forEach(param => {
              const color = param.color
              const name = param.seriesName
              const value = param.value
              content += `
                <div style="display: flex; align-items: center; margin-bottom: 4px;">
                  <span style="display: inline-block; width: 10px; height: 10px; background: ${color}; border-radius: 50%; margin-right: 8px;"></span>
                  ${name}: <span style="color: ${color}; font-weight: bold;">${value}人</span>
                </div>
              `
            })

            content += '</div>'
            return content
          }
        },
        legend: {
          data: ['会员数量', '小程序用户'],
          top: 40,
          textStyle: {
            color: '#666',
            fontSize: 12
          },
          itemGap: 20
        },
        grid: {
          left: '8%',
          right: '8%',
          bottom: '20%',
          top: '20%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          boundaryGap: false,
          data: displayLabels,
          axisLine: {
            lineStyle: {
              color: '#d0d0d0',
              width: 2
            }
          },
          axisLabel: {
            color: '#666',
            fontSize: 11,
            margin: 10,
            rotate: hasAnyData && labels && labels.length > 10 ? 45 : 0
          },
          axisTick: {
            alignWithLabel: true,
            lineStyle: {
              color: '#d0d0d0'
            }
          }
        },
        yAxis: {
          type: 'value',
          name: '用户数量 (人)',
          nameLocation: 'middle',
          nameGap: 50,
          nameTextStyle: {
            color: '#666',
            fontSize: 12,
            fontWeight: 'bold'
          },
          axisLine: {
            lineStyle: {
              color: '#d0d0d0',
              width: 2
            }
          },
          axisLabel: {
            color: '#666',
            fontSize: 11,
            formatter: value => `${Math.round(value)}人` // 确保显示整数
          },
          splitLine: {
            lineStyle: {
              color: '#f5f5f5',
              type: 'dashed'
            }
          },
          min: 0,
          max: selectedYScale.value, // 动态Y轴最大值
          interval: selectedYScale.value / 5 // 动态间隔
        },
        series: [
          {
            name: '会员数量',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: hasMemberData ? 8 : 6,
            lineStyle: {
              color: hasMemberData ? '#67C23A' : '#C0C4CC',
              width: hasMemberData ? 3 : 2,
              type: hasMemberData ? 'solid' : 'dashed'
            },
            itemStyle: {
              color: hasMemberData ? '#67C23A' : '#C0C4CC',
              borderWidth: 2,
              borderColor: '#fff'
            },
            areaStyle: hasMemberData ? {
              color: {
                type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(103, 194, 58, 0.3)' },
                  { offset: 1, color: 'rgba(103, 194, 58, 0.05)' }
                ]
              }
            } : undefined,
            data: displayMembersData
          },
          {
            name: '小程序用户',
            type: 'line',
            smooth: true,
            symbol: 'circle',
            symbolSize: hasAppUserData ? 8 : 6,
            lineStyle: {
              color: hasAppUserData ? '#E6A23C' : '#C0C4CC',
              width: hasAppUserData ? 3 : 2,
              type: hasAppUserData ? 'solid' : 'dashed'
            },
            itemStyle: {
              color: hasAppUserData ? '#E6A23C' : '#C0C4CC',
              borderWidth: 2,
              borderColor: '#fff'
            },
            areaStyle: hasAppUserData ? {
              color: {
                type: 'linear', x: 0, y: 0, x2: 0, y2: 1,
                colorStops: [
                  { offset: 0, color: 'rgba(230, 162, 60, 0.3)' },
                  { offset: 1, color: 'rgba(230, 162, 60, 0.05)' }
                ]
              }
            } : undefined,
            data: displayAppUsersData
          }
        ]
      }

      // 添加时间标识
      const timeLabel = generateTimeLabel(selectedPeriod.value)

      // 添加图表元素
      const graphicElements = [
        {
          type: 'text',
          right: 20,
          bottom: 20,
          style: {
            text: timeLabel,
            fontSize: 12,
            fill: '#666',
            textAlign: 'right'
          }
        }
      ]

      // 如果没有任何数据，添加空数据提示
      if (!hasAnyData) {
        graphicElements.push({
          type: 'text',
          left: 'center',
          top: 'middle',
          style: {
            text: '暂无用户增长数据\n请等待数据更新',
            fontSize: 16,
            fontWeight: 'bold',
            fill: '#999',
            textAlign: 'center'
          }
        })
      }

      option.graphic = graphicElements

      usersChart.setOption(option)
    } else {
      ElMessage.error('获取用户趋势数据失败：' + response.msg)
    }
  } catch (error) {
    console.error('获取用户趋势数据失败:', error)
    ElMessage.error('获取用户趋势数据失败，请稍后重试')
  }
}

// Handler for period change
const handlePeriodChange = async (period) => {
  await loadSystemStatistics()
  await updateRevenueChart()
  await updateUsersChart()
}

// Handler for Y-axis scale change
const changeYAxisScale = async (scale) => {
  selectedYScale.value = scale
  await updateUsersChart()
}

// Generate time label based on selected period
const generateTimeLabel = (period) => {
  const now = new Date()
  const currentYear = now.getFullYear()
  const currentMonth = now.getMonth() + 1
  const currentDate = now.getDate()

  switch (period) {
    case 'week':
      // 近7天：显示日期范围
      const weekStart = new Date(now)
      weekStart.setDate(now.getDate() - 6)
      const startMonth = weekStart.getMonth() + 1
      const startDate = weekStart.getDate()
      const startYear = weekStart.getFullYear()

      if (startYear === currentYear && startMonth === currentMonth) {
        // 同年同月
        return `${currentYear}年${currentMonth}月${startDate}-${currentDate}日`
      } else if (startYear === currentYear) {
        // 同年跨月
        return `${currentYear}年${startMonth}月${startDate}日-${currentMonth}月${currentDate}日`
      } else {
        // 跨年
        return `${startYear}年${startMonth}月${startDate}日-${currentYear}年${currentMonth}月${currentDate}日`
      }

    case 'month':
      // 近一月：显示日期范围
      const monthStart = new Date(now)
      monthStart.setDate(now.getDate() - 29)
      const mStartMonth = monthStart.getMonth() + 1
      const mStartDate = monthStart.getDate()
      const mStartYear = monthStart.getFullYear()

      if (mStartYear === currentYear && mStartMonth === currentMonth) {
        // 同年同月
        return `${currentYear}年${currentMonth}月${mStartDate}-${currentDate}日`
      } else if (mStartYear === currentYear) {
        // 同年跨月
        return `${currentYear}年${mStartMonth}月${mStartDate}日-${currentMonth}月${currentDate}日`
      } else {
        // 跨年
        return `${mStartYear}年${mStartMonth}月${mStartDate}日-${currentYear}年${currentMonth}月${currentDate}日`
      }

    case 'halfYear':
      // 近半年：显示月份范围，以本月为终点
      const halfYearStart = new Date(now)
      halfYearStart.setMonth(now.getMonth() - 5)
      const hStartMonth = halfYearStart.getMonth() + 1
      const hStartYear = halfYearStart.getFullYear()

      if (hStartYear === currentYear) {
        // 同年
        return `${currentYear}年${hStartMonth}-${currentMonth}月`
      } else {
        // 跨年
        return `${hStartYear}年${hStartMonth}月-${currentYear}年${currentMonth}月`
      }

    case 'year':
      // 近一年：显示月份范围，以本月为终点
      const yearStart = new Date(now)
      yearStart.setMonth(now.getMonth() - 11)
      const yStartMonth = yearStart.getMonth() + 1
      const yStartYear = yearStart.getFullYear()

      if (yStartYear === currentYear) {
        // 同年（不太可能，但保险起见）
        return `${currentYear}年${yStartMonth}-${currentMonth}月`
      } else {
        // 跨年
        return `${yStartYear}年${yStartMonth}月-${currentYear}年${currentMonth}月`
      }

    default:
      return `${currentYear}年${currentMonth}月`
  }
}

// Handler for revenue Y-axis scale change
const changeRevenueYAxisScale = async (scale) => {
  selectedRevenueYScale.value = scale
  await updateRevenueChart()
}

// Handler for warehouse change
const handleWarehouseChange = async (warehouseId) => {
  await loadWarehouseStatistics(warehouseId)
  // 场库变化时，上方卡片数据不变，只更新图表数据
  await updateRevenueChart()
  await updateUsersChart()
}

// Handler for window resize
const handleResize = () => {
  if (revenueChart) revenueChart.resize()
  if (usersChart) usersChart.resize()
}

// On component mount
onMounted(async () => {
  await loadWarehouseList()
  await loadSystemStatistics()  // 加载系统统计数据（上方卡片）

  nextTick(async () => {
    initRevenueChart()
    initUsersChart()
    await updateRevenueChart()
    await updateUsersChart()
    window.addEventListener('resize', handleResize)
  })
})

// Watch for period changes - reload system statistics
watch(selectedPeriod, async () => {
  await loadSystemStatistics()
  await updateRevenueChart()
  await updateUsersChart()
})

// Watch for warehouse changes - only update warehouse-specific data
watch(selectedWarehouse, async () => {
  if (selectedWarehouse.value) {
    await loadWarehouseStatistics(selectedWarehouse.value)
  }
  await updateRevenueChart()
  await updateUsersChart()
})

// Clean up on component unmount
onUnmounted(() => {
  if (revenueChart) revenueChart.dispose()
  if (usersChart) usersChart.dispose()
  window.removeEventListener('resize', handleResize)
})
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 24px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: calc(100vh - 50px);
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
  margin-bottom: 24px;
}

.stat-card {
  border-radius: 16px;
  border: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #409eff, #67c23a, #e6a23c, #f56c6c);
    opacity: 0;
    transition: opacity 0.3s ease;
  }

  &:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);

    &::before {
      opacity: 1;
    }
  }

  :deep(.el-card__body) {
    padding: 16px;
    background: transparent;
  }

  .stat-content {
    display: flex;
    align-items: center;
    gap: 10px;
    position: relative;
  }

  .stat-icon {
    width: 42px;
    height: 42px;
    border-radius: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-shrink: 0;
    position: relative;
    overflow: hidden;

    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      background: inherit;
      opacity: 0.1;
      border-radius: inherit;
    }

    .el-icon {
      font-size: 20px;
      color: white;
      position: relative;
      z-index: 1;
      filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
    }

    &.parking-lot {
      background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
      box-shadow: 0 8px 20px rgba(64, 158, 255, 0.3);
    }

    &.parking-space {
      background: linear-gradient(135deg, #f56c6c 0%, #ff8a8a 100%);
      box-shadow: 0 8px 20px rgba(245, 108, 108, 0.3);
    }

    &.members {
      background: linear-gradient(135deg, #67c23a 0%, #85d65a 100%);
      box-shadow: 0 8px 20px rgba(103, 194, 58, 0.3);
    }

    &.revenue {
      background: linear-gradient(135deg, #e6a23c 0%, #f0b659 100%);
      box-shadow: 0 8px 20px rgba(230, 162, 60, 0.3);
    }

    &.app-users {
      background: linear-gradient(135deg, #909399 0%, #a6a9ad 100%);
      box-shadow: 0 8px 20px rgba(144, 147, 153, 0.3);
    }
  }

  .stat-info {
    flex: 1;

    .stat-label {
      font-size: 12px;
      color: #8492a6;
      margin-bottom: 4px;
      font-weight: 500;
      letter-spacing: 0.5px;
    }

    .stat-value {
      font-size: 24px;
      font-weight: 800;
      color: #2c3e50;
      line-height: 1.1;
      margin-bottom: 4px;
      background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
      -webkit-background-clip: text;
      -webkit-text-fill-color: transparent;
      background-clip: text;
    }

    .stat-change {
      display: flex;
      align-items: center;
      font-size: 11px;
      font-weight: 600;
      padding: 2px 6px;
      border-radius: 8px;
      width: fit-content;

      .el-icon {
        margin-right: 3px;
        font-size: 12px;
      }

      &.positive {
        color: #67c23a;
        background: rgba(103, 194, 58, 0.1);
        border: 1px solid rgba(103, 194, 58, 0.2);
      }

      &.negative {
        color: #f56c6c;
        background: rgba(245, 108, 108, 0.1);
        border: 1px solid rgba(245, 108, 108, 0.2);
      }
    }
  }

  &.revenue-card {
    grid-column: span 1 / -1;
    background: linear-gradient(135deg, #fff7e8 0%, #ffffff 100%);

    @media (min-width: 1400px) {
      grid-column: span 2 / -1;
    }

    @media (max-width: 1399px) and (min-width: 992px) {
      grid-column: span 3 / -1;
    }

    .stat-info .stat-value {
      font-size: 32px;
      color: #e6a23c;
    }
  }
}

.control-panel {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  border-radius: 16px;
  margin-bottom: 32px;
  border: none;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);

  :deep(.el-card__body) {
    padding: 24px;
  }

  .controls-container {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 20px;
  }

  .control-item {
    display: flex;
    align-items: center;
  }

  .warehouse-selector {
    flex-shrink: 0;
  }

  .time-selector {
    flex-shrink: 0;
    margin-left: auto;
  }

  .selector-label {
    font-size: 14px;
    color: #606266;
    margin-right: 10px;
    white-space: nowrap;
  }
}

.warehouse-detail-wrapper {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 16px 32px;
  min-height: 60px;
  background: linear-gradient(135deg, rgba(64, 158, 255, 0.05) 0%, rgba(103, 194, 58, 0.05) 100%);
  border-radius: 12px;
  border: 1px solid rgba(64, 158, 255, 0.1);
  margin: 0 16px;

  .warehouse-title {
    font-size: 16px;
    font-weight: 700;
    color: #2c3e50;
    margin-bottom: 12px;
    white-space: nowrap;
    text-align: center;
    background: linear-gradient(135deg, #409eff 0%, #67c23a 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .warehouse-stats {
    display: flex;
    gap: 32px;
    flex-wrap: wrap;
    justify-content: center;
  }

  .warehouse-stat-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
    padding: 8px 16px;
    background: rgba(255, 255, 255, 0.8);
    border-radius: 8px;
    border: 1px solid rgba(64, 158, 255, 0.1);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-2px);
      box-shadow: 0 4px 12px rgba(64, 158, 255, 0.2);
    }

    .label {
      font-size: 12px;
      color: #8492a6;
      white-space: nowrap;
      font-weight: 500;
    }

    .value {
      font-size: 20px;
      font-weight: 700;
      color: #409eff;
      text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
    }
  }
}


.charts-section {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;

  @media (max-width: 1200px) {
    grid-template-columns: 1fr;
  }
}

.chart-container {
  border-radius: 16px;
  border: none;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.08);
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 40px rgba(0, 0, 0, 0.12);
  }

  :deep(.el-card__header) {
    border-bottom: 1px solid #f0f2f5;
  }

  .chart-title {
    font-size: 16px;
    font-weight: 600;
    color: #303133;
  }

  .chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  .chart-controls {
    display: flex;
    align-items: center;
    gap: 12px;

    .control-label {
      font-size: 14px;
      color: #666;
      font-weight: 500;
    }

    .y-axis-controls {
      .el-button {
        padding: 4px 12px;
        font-size: 12px;
      }
    }
  }
}

@media (max-width: 1200px) {
  .controls-container {
    .time-selector {
      margin-left: 0;
    }

    .warehouse-detail-wrapper {
      width: 100%;
      margin-top: 16px;
      padding: 16px 0;
      border-top: 1px solid #f0f2f5;
    }
  }
}


@media (max-width: 992px) {
  .charts-section {
    grid-template-columns: 1fr;
  }

  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  }

  .stat-card.revenue-card {
    grid-column: span 1 / -1;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .controls-container {
    flex-direction: column;
    align-items: stretch;

    .time-selector {
      justify-content: center;
    }

    .warehouse-detail-wrapper {
      margin-top: 20px;
      padding-top: 20px;
    }
  }
}

/* 星空主题特定样式 */
html.dark .dashboard-container {
  background: transparent !important;
  position: relative;

  // 统计卡片星空样式增强
  .stat-card {
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(100, 200, 255, 0.2) !important;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1),
      0 0 20px rgba(100, 200, 255, 0.1) !important;

    &:hover {
      border-color: rgba(100, 200, 255, 0.4) !important;
      box-shadow:
        0 12px 40px rgba(0, 0, 0, 0.4),
        0 0 30px rgba(100, 200, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2) !important;
      transform: translateY(-4px) scale(1.02);
    }

    .stat-label {
      color: #e6e6fa !important; /* 淡紫色 */
      text-shadow: 0 0 5px rgba(230, 230, 250, 0.4);
      font-weight: 500;
    }

    .stat-value {
      color: #f8f8ff !important; /* 月光白 */
      text-shadow: 0 0 10px rgba(248, 248, 255, 0.6);
      font-weight: 700;
    }

    .stat-change {
      color: #dda0dd !important; /* 梅花色 */
      text-shadow: 0 0 5px rgba(221, 160, 221, 0.4);

      &.positive {
        color: #98fb98 !important; /* 淡绿色 */
        text-shadow: 0 0 5px rgba(152, 251, 152, 0.4);
      }

      &.negative {
        color: #ffc0cb !important; /* 粉色 */
        text-shadow: 0 0 5px rgba(255, 192, 203, 0.4);
      }
    }
  }

  // 图标星空效果增强
  .stat-icon {
    .el-icon {
      transition: all 0.3s ease;

      &:hover {
        transform: scale(1.2) rotate(5deg);
      }
    }

    &.parking-lot .el-icon {
      color: #81c784 !important;
      filter: drop-shadow(0 0 12px rgba(129, 199, 132, 0.8));
    }

    &.parking-space .el-icon {
      color: #64b5f6 !important;
      filter: drop-shadow(0 0 12px rgba(100, 181, 246, 0.8));
    }

    &.members .el-icon {
      color: #ba68c8 !important;
      filter: drop-shadow(0 0 12px rgba(186, 104, 200, 0.8));
    }

    &.revenue .el-icon {
      color: #ffb74d !important;
      filter: drop-shadow(0 0 12px rgba(255, 183, 77, 0.8));
    }

    &.app-users .el-icon {
      color: #f06292 !important;
      filter: drop-shadow(0 0 12px rgba(240, 98, 146, 0.8));
    }
  }

  // 控制面板星空样式
  .controls-container {
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(100, 200, 255, 0.2);
    border-radius: 16px;
    padding: 20px;
    margin-bottom: 24px;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    .control-label {
      color: #e6e6fa !important; /* 淡紫色 */
      text-shadow: 0 0 5px rgba(230, 230, 250, 0.4);
      font-weight: 500;
    }
  }

  // 图表容器星空样式
  .chart-card {
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(100, 200, 255, 0.2) !important;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    .chart-title {
      color: #f8f8ff !important; /* 月光白 */
      text-shadow: 0 0 8px rgba(248, 248, 255, 0.5);
      font-weight: 600;
    }

    .chart-subtitle {
      color: #e6e6fa !important; /* 淡紫色 */
      text-shadow: 0 0 5px rgba(230, 230, 250, 0.4);
    }
  }

  // 仓库详情星空样式
  .warehouse-detail {
    background: rgba(255, 255, 255, 0.05) !important;
    backdrop-filter: blur(15px);
    border: 1px solid rgba(100, 200, 255, 0.2);
    border-radius: 16px;
    box-shadow:
      0 8px 32px rgba(0, 0, 0, 0.3),
      inset 0 1px 0 rgba(255, 255, 255, 0.1);

    .detail-title {
      color: #f8f8ff !important; /* 月光白 */
      text-shadow: 0 0 8px rgba(248, 248, 255, 0.5);
      font-weight: 600;
    }

    .detail-content {
      color: #e6e6fa !important; /* 淡紫色 */
      text-shadow: 0 0 5px rgba(230, 230, 250, 0.4);
    }
  }
}
</style>
