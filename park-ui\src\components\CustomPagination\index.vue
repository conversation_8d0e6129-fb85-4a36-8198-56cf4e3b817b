<template>
  <div class="custom-pagination-wrapper" v-if="showPagination">
    <el-pagination
      :current-page="currentPage"
      :page-size="pageSize"
      :page-sizes="pageSizes"
      :total="total"
      :layout="layout"
      :background="background"
      :small="small"
      :disabled="disabled"
      :hide-on-single-page="hideOnSinglePage"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
      class="custom-pagination"
    />
  </div>
</template>

<script setup name="CustomPagination">
/**
 * 自定义分页组件
 * 基于 Element Plus 的 el-pagination 组件封装
 * 提供更灵活的配置和统一的样式
 */

const props = defineProps({
  // 当前页码
  currentPage: {
    type: Number,
    default: 1,
  },
  // 每页显示条目个数
  pageSize: {
    type: Number,
    default: 10,
  },
  // 每页显示个数选择器的选项设置
  pageSizes: {
    type: Array,
    default: () => [10, 20, 50, 100],
  },
  // 总条目数
  total: {
    type: Number,
    required: true,
  },
  // 组件布局，子组件名用逗号分隔
  layout: {
    type: String,
    default: "total, sizes, prev, pager, next, jumper",
  },
  // 是否为分页按钮添加背景色
  background: {
    type: Boolean,
    default: true,
  },
  // 是否使用小型分页样式
  small: {
    type: Boolean,
    default: false,
  },
  // 是否禁用分页
  disabled: {
    type: Boolean,
    default: false,
  },
  // 只有一页时是否隐藏
  hideOnSinglePage: {
    type: Boolean,
    default: false,
  },
  // 是否显示分页组件
  show: {
    type: Boolean,
    default: true,
  },
});

const emit = defineEmits([
  "update:currentPage",
  "update:pageSize",
  "pagination",
  "size-change",
  "current-change",
]);

// 计算是否显示分页
const showPagination = computed(() => {
  return props.show && props.total > 0;
});

// 处理每页条数改变
const handleSizeChange = (size) => {
  emit("update:pageSize", size);
  emit("size-change", size);
  emit("pagination", { page: props.currentPage, limit: size });
};

// 处理当前页改变
const handleCurrentChange = (page) => {
  emit("update:currentPage", page);
  emit("current-change", page);
  emit("pagination", { page, limit: props.pageSize });
};
</script>

<style scoped>
.custom-pagination-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 8px 0 rgba(0, 0, 0, 0.06);
  margin-top: 16px;
}

.custom-pagination {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .custom-pagination-wrapper {
    padding: 12px;
  }

  .custom-pagination :deep(.el-pagination__sizes),
  .custom-pagination :deep(.el-pagination__jump) {
    display: none;
  }
}

/* 自定义分页按钮样式 */
.custom-pagination :deep(.el-pagination .btn-prev),
.custom-pagination :deep(.el-pagination .btn-next) {
  border-radius: 4px;
}

.custom-pagination :deep(.el-pagination .el-pager li) {
  border-radius: 4px;
  margin: 0 2px;
}

.custom-pagination :deep(.el-pagination .el-pager li.is-active) {
  background-color: #409eff;
  color: #ffffff;
}

/* 每页条数选择器样式 */
.custom-pagination :deep(.el-pagination__sizes .el-select .el-input) {
  width: 100px;
}

/* 跳转输入框样式 */
.custom-pagination :deep(.el-pagination__jump .el-input) {
  width: 50px;
}

/* 深黑星空主题样式 */
html.dark .custom-pagination-wrapper {
  background: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(248, 248, 255, 0.2);
  box-shadow:
    0 4px 16px rgba(0, 0, 0, 0.3),
    0 0 20px rgba(248, 248, 255, 0.1);
}

html.dark .custom-pagination :deep(.el-pagination .el-pager li.is-active) {
  background: rgba(248, 248, 255, 0.15) !important; /* 月光白激活背景 */
  color: #f8f8ff !important; /* 月光白文字 */
  border-color: rgba(248, 248, 255, 0.6) !important;
  text-shadow: 0 0 10px rgba(248, 248, 255, 0.8);
  box-shadow:
    0 0 15px rgba(248, 248, 255, 0.3),
    inset 0 1px 0 rgba(248, 248, 255, 0.2);
}
</style>
