2025-08-01 00:14:44.220 INFO [MetricWriter] Removing metric file: F:\parking\park-api\logs\sentinel\park-system-metrics.log.2025-07-28
2025-08-01 00:14:44.230 INFO [MetricWriter] Removing metric index file: F:\parking\park-api\logs\sentinel\park-system-metrics.log.2025-07-28.idx
2025-08-01 00:14:44.231 INFO [MetricWriter] New metric file created: logs/sentinel\park-system-metrics.log.2025-08-01
2025-08-01 00:14:44.231 INFO [MetricWriter] New metric index file created: logs/sentinel\park-system-metrics.log.2025-08-01.idx
2025-08-01 00:56:34.999 INFO Add child </agreement> to node <sentinel_spring_web_context>
2025-08-01 00:56:35.308 INFO Add child </operlog> to node <sentinel_spring_web_context>
2025-08-01 02:21:52.153 INFO Add child </dashboard/warehouse/{warehouseId}> to node <sentinel_spring_web_context>
2025-08-01 02:23:11.742 INFO Add child </platform/warehouse/list> to node <sentinel_spring_web_context>
2025-08-01 02:23:11.750 INFO Add child </platform/operator/optionSelect> to node <sentinel_spring_web_context>
2025-08-01 02:23:11.757 INFO Add child </area/provinces> to node <sentinel_spring_web_context>
2025-08-01 02:23:11.770 INFO Add child </platform/warehouse/treeList> to node <sentinel_spring_web_context>
2025-08-01 09:18:19.550 INFO App name resolved from property csp.sentinel.app.name: park-gateway
2025-08-01 09:18:19.551 INFO [SentinelConfig] Application type resolved: 11
2025-08-01 09:21:21.374 INFO [GatewayRuleManager] No gateway rules, clearing parameter metrics of previous rules
2025-08-01 09:21:21.375 INFO [GatewayRuleManager] Gateway flow rules loaded: {}
2025-08-01 09:21:21.420 INFO Add child <sentinel_default_context> to node <machine-root>
2025-08-01 09:21:21.420 INFO Add child <sentinel_gateway_context$$route$$park-auth> to node <machine-root>
2025-08-01 09:21:21.432 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.transport.init.CommandCenterInitFunc, aliasName=com.alibaba.csp.sentinel.transport.init.CommandCenterInitFunc, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.434 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.transport.init.HeartbeatSenderInitFunc, aliasName=com.alibaba.csp.sentinel.transport.init.HeartbeatSenderInitFunc, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.437 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.metric.extension.MetricCallbackInit, aliasName=com.alibaba.csp.sentinel.metric.extension.MetricCallbackInit, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.439 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.init.ParamFlowStatisticSlotCallbackInit, aliasName=com.alibaba.csp.sentinel.init.ParamFlowStatisticSlotCallbackInit, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.442 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.cluster.server.init.DefaultClusterServerInitFunc, aliasName=com.alibaba.csp.sentinel.cluster.server.init.DefaultClusterServerInitFunc, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.446 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.cluster.client.init.DefaultClusterClientInitFunc, aliasName=com.alibaba.csp.sentinel.cluster.client.init.DefaultClusterClientInitFunc, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.448 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.transport.init.CommandCenterInitFunc
2025-08-01 09:21:21.449 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.transport.init.HeartbeatSenderInitFunc
2025-08-01 09:21:21.449 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.metric.extension.MetricCallbackInit
2025-08-01 09:21:21.449 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.init.ParamFlowStatisticSlotCallbackInit
2025-08-01 09:21:21.449 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.cluster.server.init.DefaultClusterServerInitFunc
2025-08-01 09:21:21.449 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.cluster.client.init.DefaultClusterClientInitFunc
2025-08-01 09:21:21.454 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.transport.CommandCenter, provider=com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter, aliasName=com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.457 INFO [CommandCenterProvider] CommandCenter resolved: com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter
2025-08-01 09:21:21.462 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.BasicInfoCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.BasicInfoCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.463 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchActiveRuleCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchActiveRuleCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.464 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchClusterNodeByIdCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchClusterNodeByIdCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.466 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchClusterNodeHumanCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchClusterNodeHumanCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.467 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchJsonTreeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchJsonTreeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.468 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchOriginCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchOriginCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.469 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchSimpleClusterNodeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchSimpleClusterNodeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.470 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchSystemStatusCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchSystemStatusCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.471 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchTreeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchTreeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.471 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.ModifyRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.ModifyRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.472 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.OnOffGetCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.OnOffGetCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.472 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.OnOffSetCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.OnOffSetCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.474 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.SendMetricCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.SendMetricCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.474 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.VersionCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.VersionCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.475 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.cluster.FetchClusterModeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.cluster.FetchClusterModeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.476 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.cluster.ModifyClusterModeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.cluster.ModifyClusterModeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.477 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.ApiCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.ApiCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.478 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.GetParamFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.GetParamFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.479 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.ModifyParamFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.ModifyParamFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.481 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterServerFlowConfigHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterServerFlowConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.482 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.483 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterParamFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterParamFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.485 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterServerConfigHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterServerConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.486 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterServerTransportConfigHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterServerTransportConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.487 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyServerNamespaceSetHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyServerNamespaceSetHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.488 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.489 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterParamFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterParamFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.490 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterServerInfoCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterServerInfoCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.491 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterMetricCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterMetricCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.492 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.ModifyClusterClientConfigHandler, aliasName=com.alibaba.csp.sentinel.command.handler.ModifyClusterClientConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.493 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchClusterClientConfigHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchClusterClientConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.495 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.adapter.gateway.common.command.UpdateGatewayApiDefinitionGroupCommandHandler, aliasName=com.alibaba.csp.sentinel.adapter.gateway.common.command.UpdateGatewayApiDefinitionGroupCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.496 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.adapter.gateway.common.command.UpdateGatewayRuleCommandHandler, aliasName=com.alibaba.csp.sentinel.adapter.gateway.common.command.UpdateGatewayRuleCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.497 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.adapter.gateway.common.command.GetGatewayApiDefinitionGroupCommandHandler, aliasName=com.alibaba.csp.sentinel.adapter.gateway.common.command.GetGatewayApiDefinitionGroupCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.498 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.adapter.gateway.common.command.GetGatewayRuleCommandHandler, aliasName=com.alibaba.csp.sentinel.adapter.gateway.common.command.GetGatewayRuleCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.500 WARNING No SPI configuration file, filename=META-INF/services/com.alibaba.csp.sentinel.command.CommandHandlerInterceptor, classloader=sun.misc.Launcher$AppClassLoader@18b4aac2
2025-08-01 09:21:21.501 INFO [CommandCenterInit] Starting command center: com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter
2025-08-01 09:21:21.502 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.transport.init.CommandCenterInitFunc with order -1
2025-08-01 09:21:21.503 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.transport.HeartbeatSender, provider=com.alibaba.csp.sentinel.transport.heartbeat.SimpleHttpHeartbeatSender, aliasName=com.alibaba.csp.sentinel.transport.heartbeat.SimpleHttpHeartbeatSender, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.510 WARNING [SimpleHttpHeartbeatSender] Dashboard server address not configured or not available
2025-08-01 09:21:21.510 INFO [HeartbeatSenderProvider] HeartbeatSender activated: com.alibaba.csp.sentinel.transport.heartbeat.SimpleHttpHeartbeatSender
2025-08-01 09:21:21.511 INFO [HeartbeatSenderInit] Heartbeat interval not configured in config property or invalid, using sender default: 10000
2025-08-01 09:21:21.512 INFO [HeartbeatSenderInit] HeartbeatSender started: com.alibaba.csp.sentinel.transport.heartbeat.SimpleHttpHeartbeatSender
2025-08-01 09:21:21.512 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.transport.init.HeartbeatSenderInitFunc with order -1
2025-08-01 09:21:21.520 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.cluster.client.init.DefaultClusterClientInitFunc with order 0
2025-08-01 09:21:21.523 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.metric.extension.MetricCallbackInit with order **********
2025-08-01 09:21:21.524 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.init.ParamFlowStatisticSlotCallbackInit with order **********
2025-08-01 09:21:21.531 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.cluster.server.processor.RequestProcessor, provider=com.alibaba.csp.sentinel.cluster.server.processor.FlowRequestProcessor, aliasName=com.alibaba.csp.sentinel.cluster.server.processor.FlowRequestProcessor, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.532 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.cluster.server.processor.RequestProcessor, provider=com.alibaba.csp.sentinel.cluster.server.processor.ParamFlowRequestProcessor, aliasName=com.alibaba.csp.sentinel.cluster.server.processor.ParamFlowRequestProcessor, isSingleton=true, isDefault=false, order=0
2025-08-01 09:21:21.535 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.cluster.TokenService, provider=com.alibaba.csp.sentinel.cluster.flow.DefaultTokenService, aliasName=com.alibaba.csp.sentinel.cluster.flow.DefaultTokenService, isSingleton=true, isDefault=true, order=0
2025-08-01 09:21:21.535 INFO [TokenServiceProvider] Global token service resolved: com.alibaba.csp.sentinel.cluster.flow.DefaultTokenService
2025-08-01 09:21:21.535 INFO [DefaultClusterServerInitFunc] Default entity codec and processors registered
2025-08-01 09:21:21.535 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.cluster.server.init.DefaultClusterServerInitFunc with order **********
2025-08-01 09:21:21.537 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.SlotChainBuilder, provider=com.alibaba.csp.sentinel.slots.DefaultSlotChainBuilder, aliasName=com.alibaba.csp.sentinel.slots.DefaultSlotChainBuilder, isSingleton=true, isDefault=true, order=0
2025-08-01 09:21:21.538 INFO [SlotChainProvider] Global slot chain builder resolved: com.alibaba.csp.sentinel.slots.DefaultSlotChainBuilder
2025-08-01 09:21:21.542 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.nodeselector.NodeSelectorSlot, aliasName=com.alibaba.csp.sentinel.slots.nodeselector.NodeSelectorSlot, isSingleton=false, isDefault=false, order=-10000
2025-08-01 09:21:21.542 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.clusterbuilder.ClusterBuilderSlot, aliasName=com.alibaba.csp.sentinel.slots.clusterbuilder.ClusterBuilderSlot, isSingleton=false, isDefault=false, order=-9000
2025-08-01 09:21:21.543 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.logger.LogSlot, aliasName=com.alibaba.csp.sentinel.slots.logger.LogSlot, isSingleton=true, isDefault=false, order=-8000
2025-08-01 09:21:21.543 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.statistic.StatisticSlot, aliasName=com.alibaba.csp.sentinel.slots.statistic.StatisticSlot, isSingleton=true, isDefault=false, order=-7000
2025-08-01 09:21:21.544 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.block.authority.AuthoritySlot, aliasName=com.alibaba.csp.sentinel.slots.block.authority.AuthoritySlot, isSingleton=true, isDefault=false, order=-6000
2025-08-01 09:21:21.544 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.system.SystemSlot, aliasName=com.alibaba.csp.sentinel.slots.system.SystemSlot, isSingleton=true, isDefault=false, order=-5000
2025-08-01 09:21:21.545 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.block.flow.FlowSlot, aliasName=com.alibaba.csp.sentinel.slots.block.flow.FlowSlot, isSingleton=true, isDefault=false, order=-2000
2025-08-01 09:21:21.546 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.block.degrade.DegradeSlot, aliasName=com.alibaba.csp.sentinel.slots.block.degrade.DegradeSlot, isSingleton=true, isDefault=false, order=-1000
2025-08-01 09:21:21.546 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowSlot, aliasName=com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowSlot, isSingleton=true, isDefault=false, order=-3000
2025-08-01 09:21:21.548 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.adapter.gateway.common.slot.GatewayFlowSlot, aliasName=com.alibaba.csp.sentinel.adapter.gateway.common.slot.GatewayFlowSlot, isSingleton=true, isDefault=false, order=-4000
2025-08-01 09:21:21.553 INFO Add child <park-auth> to node <sentinel_gateway_context$$route$$park-auth>
2025-08-01 09:21:21.555 INFO [AuthorityRuleManager] Authority rules loaded: {}
2025-08-01 09:21:21.560 INFO [SystemRuleManager] Current system check status: false, highestSystemLoad: 1.797693e+308, highestCpuUsage: 1.797693e+308, maxRt: 9223372036854775807, maxThread: 9223372036854775807, maxQps: 1.797693e+308
2025-08-01 09:21:21.563 INFO [ParamFlowRuleManager] No parameter flow rules, clearing all parameter metrics
2025-08-01 09:21:21.563 INFO [ParamFlowRuleManager] Parameter flow rules received: {}
2025-08-01 09:21:21.566 INFO [FlowRuleManager] Flow rules loaded: {}
2025-08-01 09:21:21.569 INFO [MetricWriter] Creating new MetricWriter, singleFileSize=52428800, totalFileCount=6
2025-08-01 09:21:21.572 INFO [DegradeRuleManager] Degrade rules loaded: {}
2025-08-01 09:21:21.574 WARNING No SPI configuration file, filename=META-INF/services/com.alibaba.csp.sentinel.metric.extension.MetricExtension, classloader=sun.misc.Launcher$AppClassLoader@18b4aac2
2025-08-01 09:21:21.574 INFO [MetricExtensionProvider] No existing MetricExtension found
2025-08-01 09:21:22.591 INFO [MetricWriter] Removing metric file: F:\parking\park-api\logs\sentinel\park-gateway-metrics.log.2025-07-31
2025-08-01 09:21:22.591 INFO [MetricWriter] Removing metric index file: F:\parking\park-api\logs\sentinel\park-gateway-metrics.log.2025-07-31.idx
2025-08-01 09:21:22.592 INFO [MetricWriter] New metric file created: logs/sentinel\park-gateway-metrics.log.2025-08-01.1
2025-08-01 09:21:22.592 INFO [MetricWriter] New metric index file created: logs/sentinel\park-gateway-metrics.log.2025-08-01.1.idx
2025-08-01 09:21:24.274 INFO Add child <sentinel_gateway_context$$route$$park-system> to node <machine-root>
2025-08-01 09:21:24.275 INFO Add child <park-system> to node <sentinel_gateway_context$$route$$park-system>
2025-08-01 10:18:55.103 INFO App name resolved from property csp.sentinel.app.name: park-gateway
2025-08-01 10:18:55.104 INFO [SentinelConfig] Application type resolved: 11
2025-08-01 10:22:09.401 INFO [GatewayRuleManager] No gateway rules, clearing parameter metrics of previous rules
2025-08-01 10:22:09.402 INFO [GatewayRuleManager] Gateway flow rules loaded: {}
2025-08-01 10:22:09.711 INFO Add child <sentinel_default_context> to node <machine-root>
2025-08-01 10:22:09.711 INFO Add child <sentinel_gateway_context$$route$$park-system> to node <machine-root>
2025-08-01 10:22:09.715 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.transport.init.CommandCenterInitFunc, aliasName=com.alibaba.csp.sentinel.transport.init.CommandCenterInitFunc, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.716 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.transport.init.HeartbeatSenderInitFunc, aliasName=com.alibaba.csp.sentinel.transport.init.HeartbeatSenderInitFunc, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.718 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.metric.extension.MetricCallbackInit, aliasName=com.alibaba.csp.sentinel.metric.extension.MetricCallbackInit, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.718 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.init.ParamFlowStatisticSlotCallbackInit, aliasName=com.alibaba.csp.sentinel.init.ParamFlowStatisticSlotCallbackInit, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.720 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.cluster.server.init.DefaultClusterServerInitFunc, aliasName=com.alibaba.csp.sentinel.cluster.server.init.DefaultClusterServerInitFunc, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.722 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.cluster.client.init.DefaultClusterClientInitFunc, aliasName=com.alibaba.csp.sentinel.cluster.client.init.DefaultClusterClientInitFunc, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.723 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.transport.init.CommandCenterInitFunc
2025-08-01 10:22:09.723 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.transport.init.HeartbeatSenderInitFunc
2025-08-01 10:22:09.723 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.metric.extension.MetricCallbackInit
2025-08-01 10:22:09.723 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.init.ParamFlowStatisticSlotCallbackInit
2025-08-01 10:22:09.723 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.cluster.server.init.DefaultClusterServerInitFunc
2025-08-01 10:22:09.723 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.cluster.client.init.DefaultClusterClientInitFunc
2025-08-01 10:22:09.726 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.transport.CommandCenter, provider=com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter, aliasName=com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.727 INFO [CommandCenterProvider] CommandCenter resolved: com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter
2025-08-01 10:22:09.729 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.BasicInfoCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.BasicInfoCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.730 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchActiveRuleCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchActiveRuleCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.731 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchClusterNodeByIdCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchClusterNodeByIdCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.731 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchClusterNodeHumanCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchClusterNodeHumanCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.732 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchJsonTreeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchJsonTreeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.732 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchOriginCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchOriginCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.733 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchSimpleClusterNodeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchSimpleClusterNodeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.733 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchSystemStatusCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchSystemStatusCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.733 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchTreeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchTreeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.734 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.ModifyRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.ModifyRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.735 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.OnOffGetCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.OnOffGetCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.735 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.OnOffSetCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.OnOffSetCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.735 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.SendMetricCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.SendMetricCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.735 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.VersionCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.VersionCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.736 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.cluster.FetchClusterModeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.cluster.FetchClusterModeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.736 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.cluster.ModifyClusterModeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.cluster.ModifyClusterModeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.736 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.ApiCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.ApiCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.738 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.GetParamFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.GetParamFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.738 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.ModifyParamFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.ModifyParamFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.739 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterServerFlowConfigHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterServerFlowConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.739 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.740 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterParamFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterParamFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.741 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterServerConfigHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterServerConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.741 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterServerTransportConfigHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterServerTransportConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.741 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyServerNamespaceSetHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyServerNamespaceSetHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.742 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.742 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterParamFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterParamFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.742 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterServerInfoCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterServerInfoCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.743 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterMetricCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterMetricCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.743 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.ModifyClusterClientConfigHandler, aliasName=com.alibaba.csp.sentinel.command.handler.ModifyClusterClientConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.744 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchClusterClientConfigHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchClusterClientConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.745 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.adapter.gateway.common.command.UpdateGatewayApiDefinitionGroupCommandHandler, aliasName=com.alibaba.csp.sentinel.adapter.gateway.common.command.UpdateGatewayApiDefinitionGroupCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.745 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.adapter.gateway.common.command.UpdateGatewayRuleCommandHandler, aliasName=com.alibaba.csp.sentinel.adapter.gateway.common.command.UpdateGatewayRuleCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.745 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.adapter.gateway.common.command.GetGatewayApiDefinitionGroupCommandHandler, aliasName=com.alibaba.csp.sentinel.adapter.gateway.common.command.GetGatewayApiDefinitionGroupCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.746 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.adapter.gateway.common.command.GetGatewayRuleCommandHandler, aliasName=com.alibaba.csp.sentinel.adapter.gateway.common.command.GetGatewayRuleCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.747 WARNING No SPI configuration file, filename=META-INF/services/com.alibaba.csp.sentinel.command.CommandHandlerInterceptor, classloader=sun.misc.Launcher$AppClassLoader@18b4aac2
2025-08-01 10:22:09.748 INFO [CommandCenterInit] Starting command center: com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter
2025-08-01 10:22:09.748 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.transport.init.CommandCenterInitFunc with order -1
2025-08-01 10:22:09.750 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.transport.HeartbeatSender, provider=com.alibaba.csp.sentinel.transport.heartbeat.SimpleHttpHeartbeatSender, aliasName=com.alibaba.csp.sentinel.transport.heartbeat.SimpleHttpHeartbeatSender, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.752 WARNING [SimpleHttpHeartbeatSender] Dashboard server address not configured or not available
2025-08-01 10:22:09.752 INFO [HeartbeatSenderProvider] HeartbeatSender activated: com.alibaba.csp.sentinel.transport.heartbeat.SimpleHttpHeartbeatSender
2025-08-01 10:22:09.753 INFO [HeartbeatSenderInit] Heartbeat interval not configured in config property or invalid, using sender default: 10000
2025-08-01 10:22:09.754 INFO [HeartbeatSenderInit] HeartbeatSender started: com.alibaba.csp.sentinel.transport.heartbeat.SimpleHttpHeartbeatSender
2025-08-01 10:22:09.754 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.transport.init.HeartbeatSenderInitFunc with order -1
2025-08-01 10:22:09.757 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.cluster.client.init.DefaultClusterClientInitFunc with order 0
2025-08-01 10:22:09.759 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.metric.extension.MetricCallbackInit with order **********
2025-08-01 10:22:09.760 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.init.ParamFlowStatisticSlotCallbackInit with order **********
2025-08-01 10:22:09.765 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.cluster.server.processor.RequestProcessor, provider=com.alibaba.csp.sentinel.cluster.server.processor.FlowRequestProcessor, aliasName=com.alibaba.csp.sentinel.cluster.server.processor.FlowRequestProcessor, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.765 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.cluster.server.processor.RequestProcessor, provider=com.alibaba.csp.sentinel.cluster.server.processor.ParamFlowRequestProcessor, aliasName=com.alibaba.csp.sentinel.cluster.server.processor.ParamFlowRequestProcessor, isSingleton=true, isDefault=false, order=0
2025-08-01 10:22:09.766 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.cluster.TokenService, provider=com.alibaba.csp.sentinel.cluster.flow.DefaultTokenService, aliasName=com.alibaba.csp.sentinel.cluster.flow.DefaultTokenService, isSingleton=true, isDefault=true, order=0
2025-08-01 10:22:09.767 INFO [TokenServiceProvider] Global token service resolved: com.alibaba.csp.sentinel.cluster.flow.DefaultTokenService
2025-08-01 10:22:09.767 INFO [DefaultClusterServerInitFunc] Default entity codec and processors registered
2025-08-01 10:22:09.767 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.cluster.server.init.DefaultClusterServerInitFunc with order **********
2025-08-01 10:22:09.768 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.SlotChainBuilder, provider=com.alibaba.csp.sentinel.slots.DefaultSlotChainBuilder, aliasName=com.alibaba.csp.sentinel.slots.DefaultSlotChainBuilder, isSingleton=true, isDefault=true, order=0
2025-08-01 10:22:09.768 INFO [SlotChainProvider] Global slot chain builder resolved: com.alibaba.csp.sentinel.slots.DefaultSlotChainBuilder
2025-08-01 10:22:09.770 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.nodeselector.NodeSelectorSlot, aliasName=com.alibaba.csp.sentinel.slots.nodeselector.NodeSelectorSlot, isSingleton=false, isDefault=false, order=-10000
2025-08-01 10:22:09.770 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.clusterbuilder.ClusterBuilderSlot, aliasName=com.alibaba.csp.sentinel.slots.clusterbuilder.ClusterBuilderSlot, isSingleton=false, isDefault=false, order=-9000
2025-08-01 10:22:09.771 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.logger.LogSlot, aliasName=com.alibaba.csp.sentinel.slots.logger.LogSlot, isSingleton=true, isDefault=false, order=-8000
2025-08-01 10:22:09.771 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.statistic.StatisticSlot, aliasName=com.alibaba.csp.sentinel.slots.statistic.StatisticSlot, isSingleton=true, isDefault=false, order=-7000
2025-08-01 10:22:09.771 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.block.authority.AuthoritySlot, aliasName=com.alibaba.csp.sentinel.slots.block.authority.AuthoritySlot, isSingleton=true, isDefault=false, order=-6000
2025-08-01 10:22:09.771 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.system.SystemSlot, aliasName=com.alibaba.csp.sentinel.slots.system.SystemSlot, isSingleton=true, isDefault=false, order=-5000
2025-08-01 10:22:09.772 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.block.flow.FlowSlot, aliasName=com.alibaba.csp.sentinel.slots.block.flow.FlowSlot, isSingleton=true, isDefault=false, order=-2000
2025-08-01 10:22:09.773 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.block.degrade.DegradeSlot, aliasName=com.alibaba.csp.sentinel.slots.block.degrade.DegradeSlot, isSingleton=true, isDefault=false, order=-1000
2025-08-01 10:22:09.773 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowSlot, aliasName=com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowSlot, isSingleton=true, isDefault=false, order=-3000
2025-08-01 10:22:09.774 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.adapter.gateway.common.slot.GatewayFlowSlot, aliasName=com.alibaba.csp.sentinel.adapter.gateway.common.slot.GatewayFlowSlot, isSingleton=true, isDefault=false, order=-4000
2025-08-01 10:22:09.778 INFO Add child <park-system> to node <sentinel_gateway_context$$route$$park-system>
2025-08-01 10:22:09.779 INFO [AuthorityRuleManager] Authority rules loaded: {}
2025-08-01 10:22:09.782 INFO [SystemRuleManager] Current system check status: false, highestSystemLoad: 1.797693e+308, highestCpuUsage: 1.797693e+308, maxRt: 9223372036854775807, maxThread: 9223372036854775807, maxQps: 1.797693e+308
2025-08-01 10:22:09.783 INFO [ParamFlowRuleManager] No parameter flow rules, clearing all parameter metrics
2025-08-01 10:22:09.783 INFO [ParamFlowRuleManager] Parameter flow rules received: {}
2025-08-01 10:22:09.785 INFO [FlowRuleManager] Flow rules loaded: {}
2025-08-01 10:22:09.787 INFO [MetricWriter] Creating new MetricWriter, singleFileSize=52428800, totalFileCount=6
2025-08-01 10:22:09.789 INFO [DegradeRuleManager] Degrade rules loaded: {}
2025-08-01 10:22:09.790 WARNING No SPI configuration file, filename=META-INF/services/com.alibaba.csp.sentinel.metric.extension.MetricExtension, classloader=sun.misc.Launcher$AppClassLoader@18b4aac2
2025-08-01 10:22:09.790 INFO [MetricExtensionProvider] No existing MetricExtension found
2025-08-01 10:22:10.808 INFO [MetricWriter] Removing metric file: F:\parking\park-api\logs\sentinel\park-gateway-metrics.log.2025-07-31.1
2025-08-01 10:22:10.808 INFO [MetricWriter] Removing metric index file: F:\parking\park-api\logs\sentinel\park-gateway-metrics.log.2025-07-31.1.idx
2025-08-01 10:22:10.810 INFO [MetricWriter] New metric file created: logs/sentinel\park-gateway-metrics.log.2025-08-01.2
2025-08-01 10:22:10.810 INFO [MetricWriter] New metric index file created: logs/sentinel\park-gateway-metrics.log.2025-08-01.2.idx
2025-08-01 10:59:50.954 INFO Add child <sentinel_gateway_context$$route$$park-wx> to node <machine-root>
2025-08-01 10:59:50.961 INFO Add child <park-wx> to node <sentinel_gateway_context$$route$$park-wx>
2025-08-01 12:35:49.106 INFO Add child <sentinel_gateway_context$$route$$park-auth> to node <machine-root>
2025-08-01 12:35:49.109 INFO Add child <park-auth> to node <sentinel_gateway_context$$route$$park-auth>
2025-08-01 13:16:50.388 INFO App name resolved from property csp.sentinel.app.name: park-gateway
2025-08-01 13:16:50.389 INFO [SentinelConfig] Application type resolved: 11
2025-08-01 13:20:19.791 INFO [GatewayRuleManager] No gateway rules, clearing parameter metrics of previous rules
2025-08-01 13:20:19.792 INFO [GatewayRuleManager] Gateway flow rules loaded: {}
2025-08-01 13:20:20.134 INFO Add child <sentinel_default_context> to node <machine-root>
2025-08-01 13:20:20.134 INFO Add child <sentinel_gateway_context$$route$$park-system> to node <machine-root>
2025-08-01 13:20:20.138 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.transport.init.CommandCenterInitFunc, aliasName=com.alibaba.csp.sentinel.transport.init.CommandCenterInitFunc, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.139 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.transport.init.HeartbeatSenderInitFunc, aliasName=com.alibaba.csp.sentinel.transport.init.HeartbeatSenderInitFunc, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.140 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.metric.extension.MetricCallbackInit, aliasName=com.alibaba.csp.sentinel.metric.extension.MetricCallbackInit, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.141 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.init.ParamFlowStatisticSlotCallbackInit, aliasName=com.alibaba.csp.sentinel.init.ParamFlowStatisticSlotCallbackInit, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.143 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.cluster.server.init.DefaultClusterServerInitFunc, aliasName=com.alibaba.csp.sentinel.cluster.server.init.DefaultClusterServerInitFunc, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.145 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.init.InitFunc, provider=com.alibaba.csp.sentinel.cluster.client.init.DefaultClusterClientInitFunc, aliasName=com.alibaba.csp.sentinel.cluster.client.init.DefaultClusterClientInitFunc, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.145 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.transport.init.CommandCenterInitFunc
2025-08-01 13:20:20.146 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.transport.init.HeartbeatSenderInitFunc
2025-08-01 13:20:20.146 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.metric.extension.MetricCallbackInit
2025-08-01 13:20:20.146 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.init.ParamFlowStatisticSlotCallbackInit
2025-08-01 13:20:20.146 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.cluster.server.init.DefaultClusterServerInitFunc
2025-08-01 13:20:20.146 INFO [InitExecutor] Found init func: com.alibaba.csp.sentinel.cluster.client.init.DefaultClusterClientInitFunc
2025-08-01 13:20:20.149 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.transport.CommandCenter, provider=com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter, aliasName=com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.149 INFO [CommandCenterProvider] CommandCenter resolved: com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter
2025-08-01 13:20:20.152 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.BasicInfoCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.BasicInfoCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.153 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchActiveRuleCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchActiveRuleCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.153 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchClusterNodeByIdCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchClusterNodeByIdCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.154 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchClusterNodeHumanCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchClusterNodeHumanCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.154 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchJsonTreeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchJsonTreeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.155 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchOriginCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchOriginCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.155 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchSimpleClusterNodeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchSimpleClusterNodeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.156 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchSystemStatusCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchSystemStatusCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.156 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchTreeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchTreeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.156 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.ModifyRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.ModifyRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.157 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.OnOffGetCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.OnOffGetCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.157 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.OnOffSetCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.OnOffSetCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.157 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.SendMetricCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.SendMetricCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.158 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.VersionCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.VersionCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.158 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.cluster.FetchClusterModeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.cluster.FetchClusterModeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.159 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.cluster.ModifyClusterModeCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.cluster.ModifyClusterModeCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.159 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.ApiCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.ApiCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.160 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.GetParamFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.GetParamFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.161 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.ModifyParamFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.command.handler.ModifyParamFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.161 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterServerFlowConfigHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterServerFlowConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.162 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.162 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterParamFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterParamFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.163 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterServerConfigHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterServerConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.163 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterServerTransportConfigHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterServerTransportConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.164 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyServerNamespaceSetHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyServerNamespaceSetHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.164 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.165 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterParamFlowRulesCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.ModifyClusterParamFlowRulesCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.166 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterServerInfoCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterServerInfoCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.166 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterMetricCommandHandler, aliasName=com.alibaba.csp.sentinel.cluster.server.command.handler.FetchClusterMetricCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.166 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.ModifyClusterClientConfigHandler, aliasName=com.alibaba.csp.sentinel.command.handler.ModifyClusterClientConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.167 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.command.handler.FetchClusterClientConfigHandler, aliasName=com.alibaba.csp.sentinel.command.handler.FetchClusterClientConfigHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.168 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.adapter.gateway.common.command.UpdateGatewayApiDefinitionGroupCommandHandler, aliasName=com.alibaba.csp.sentinel.adapter.gateway.common.command.UpdateGatewayApiDefinitionGroupCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.168 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.adapter.gateway.common.command.UpdateGatewayRuleCommandHandler, aliasName=com.alibaba.csp.sentinel.adapter.gateway.common.command.UpdateGatewayRuleCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.168 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.adapter.gateway.common.command.GetGatewayApiDefinitionGroupCommandHandler, aliasName=com.alibaba.csp.sentinel.adapter.gateway.common.command.GetGatewayApiDefinitionGroupCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.169 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.command.CommandHandler, provider=com.alibaba.csp.sentinel.adapter.gateway.common.command.GetGatewayRuleCommandHandler, aliasName=com.alibaba.csp.sentinel.adapter.gateway.common.command.GetGatewayRuleCommandHandler, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.170 WARNING No SPI configuration file, filename=META-INF/services/com.alibaba.csp.sentinel.command.CommandHandlerInterceptor, classloader=sun.misc.Launcher$AppClassLoader@18b4aac2
2025-08-01 13:20:20.171 INFO [CommandCenterInit] Starting command center: com.alibaba.csp.sentinel.transport.command.SimpleHttpCommandCenter
2025-08-01 13:20:20.172 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.transport.init.CommandCenterInitFunc with order -1
2025-08-01 13:20:20.173 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.transport.HeartbeatSender, provider=com.alibaba.csp.sentinel.transport.heartbeat.SimpleHttpHeartbeatSender, aliasName=com.alibaba.csp.sentinel.transport.heartbeat.SimpleHttpHeartbeatSender, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.176 WARNING [SimpleHttpHeartbeatSender] Dashboard server address not configured or not available
2025-08-01 13:20:20.176 INFO [HeartbeatSenderProvider] HeartbeatSender activated: com.alibaba.csp.sentinel.transport.heartbeat.SimpleHttpHeartbeatSender
2025-08-01 13:20:20.176 INFO [HeartbeatSenderInit] Heartbeat interval not configured in config property or invalid, using sender default: 10000
2025-08-01 13:20:20.177 INFO [HeartbeatSenderInit] HeartbeatSender started: com.alibaba.csp.sentinel.transport.heartbeat.SimpleHttpHeartbeatSender
2025-08-01 13:20:20.177 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.transport.init.HeartbeatSenderInitFunc with order -1
2025-08-01 13:20:20.181 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.cluster.client.init.DefaultClusterClientInitFunc with order 0
2025-08-01 13:20:20.183 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.metric.extension.MetricCallbackInit with order **********
2025-08-01 13:20:20.184 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.init.ParamFlowStatisticSlotCallbackInit with order **********
2025-08-01 13:20:20.188 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.cluster.server.processor.RequestProcessor, provider=com.alibaba.csp.sentinel.cluster.server.processor.FlowRequestProcessor, aliasName=com.alibaba.csp.sentinel.cluster.server.processor.FlowRequestProcessor, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.189 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.cluster.server.processor.RequestProcessor, provider=com.alibaba.csp.sentinel.cluster.server.processor.ParamFlowRequestProcessor, aliasName=com.alibaba.csp.sentinel.cluster.server.processor.ParamFlowRequestProcessor, isSingleton=true, isDefault=false, order=0
2025-08-01 13:20:20.191 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.cluster.TokenService, provider=com.alibaba.csp.sentinel.cluster.flow.DefaultTokenService, aliasName=com.alibaba.csp.sentinel.cluster.flow.DefaultTokenService, isSingleton=true, isDefault=true, order=0
2025-08-01 13:20:20.191 INFO [TokenServiceProvider] Global token service resolved: com.alibaba.csp.sentinel.cluster.flow.DefaultTokenService
2025-08-01 13:20:20.191 INFO [DefaultClusterServerInitFunc] Default entity codec and processors registered
2025-08-01 13:20:20.191 INFO [InitExecutor] Executing com.alibaba.csp.sentinel.cluster.server.init.DefaultClusterServerInitFunc with order **********
2025-08-01 13:20:20.192 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.SlotChainBuilder, provider=com.alibaba.csp.sentinel.slots.DefaultSlotChainBuilder, aliasName=com.alibaba.csp.sentinel.slots.DefaultSlotChainBuilder, isSingleton=true, isDefault=true, order=0
2025-08-01 13:20:20.193 INFO [SlotChainProvider] Global slot chain builder resolved: com.alibaba.csp.sentinel.slots.DefaultSlotChainBuilder
2025-08-01 13:20:20.194 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.nodeselector.NodeSelectorSlot, aliasName=com.alibaba.csp.sentinel.slots.nodeselector.NodeSelectorSlot, isSingleton=false, isDefault=false, order=-10000
2025-08-01 13:20:20.195 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.clusterbuilder.ClusterBuilderSlot, aliasName=com.alibaba.csp.sentinel.slots.clusterbuilder.ClusterBuilderSlot, isSingleton=false, isDefault=false, order=-9000
2025-08-01 13:20:20.195 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.logger.LogSlot, aliasName=com.alibaba.csp.sentinel.slots.logger.LogSlot, isSingleton=true, isDefault=false, order=-8000
2025-08-01 13:20:20.195 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.statistic.StatisticSlot, aliasName=com.alibaba.csp.sentinel.slots.statistic.StatisticSlot, isSingleton=true, isDefault=false, order=-7000
2025-08-01 13:20:20.196 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.block.authority.AuthoritySlot, aliasName=com.alibaba.csp.sentinel.slots.block.authority.AuthoritySlot, isSingleton=true, isDefault=false, order=-6000
2025-08-01 13:20:20.196 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.system.SystemSlot, aliasName=com.alibaba.csp.sentinel.slots.system.SystemSlot, isSingleton=true, isDefault=false, order=-5000
2025-08-01 13:20:20.197 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.block.flow.FlowSlot, aliasName=com.alibaba.csp.sentinel.slots.block.flow.FlowSlot, isSingleton=true, isDefault=false, order=-2000
2025-08-01 13:20:20.197 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.block.degrade.DegradeSlot, aliasName=com.alibaba.csp.sentinel.slots.block.degrade.DegradeSlot, isSingleton=true, isDefault=false, order=-1000
2025-08-01 13:20:20.197 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowSlot, aliasName=com.alibaba.csp.sentinel.slots.block.flow.param.ParamFlowSlot, isSingleton=true, isDefault=false, order=-3000
2025-08-01 13:20:20.199 INFO [SpiLoader] Found SPI implementation for SPI com.alibaba.csp.sentinel.slotchain.ProcessorSlot, provider=com.alibaba.csp.sentinel.adapter.gateway.common.slot.GatewayFlowSlot, aliasName=com.alibaba.csp.sentinel.adapter.gateway.common.slot.GatewayFlowSlot, isSingleton=true, isDefault=false, order=-4000
2025-08-01 13:20:20.202 INFO Add child <park-system> to node <sentinel_gateway_context$$route$$park-system>
2025-08-01 13:20:20.203 INFO [AuthorityRuleManager] Authority rules loaded: {}
2025-08-01 13:20:20.207 INFO [SystemRuleManager] Current system check status: false, highestSystemLoad: 1.797693e+308, highestCpuUsage: 1.797693e+308, maxRt: 9223372036854775807, maxThread: 9223372036854775807, maxQps: 1.797693e+308
2025-08-01 13:20:20.208 INFO [ParamFlowRuleManager] No parameter flow rules, clearing all parameter metrics
2025-08-01 13:20:20.208 INFO [ParamFlowRuleManager] Parameter flow rules received: {}
2025-08-01 13:20:20.210 INFO [FlowRuleManager] Flow rules loaded: {}
2025-08-01 13:20:20.212 INFO [MetricWriter] Creating new MetricWriter, singleFileSize=52428800, totalFileCount=6
2025-08-01 13:20:20.214 INFO [DegradeRuleManager] Degrade rules loaded: {}
2025-08-01 13:20:20.215 WARNING No SPI configuration file, filename=META-INF/services/com.alibaba.csp.sentinel.metric.extension.MetricExtension, classloader=sun.misc.Launcher$AppClassLoader@18b4aac2
2025-08-01 13:20:20.215 INFO [MetricExtensionProvider] No existing MetricExtension found
2025-08-01 13:20:21.233 INFO [MetricWriter] Removing metric file: F:\parking\park-api\logs\sentinel\park-gateway-metrics.log.2025-07-31.2
2025-08-01 13:20:21.234 INFO [MetricWriter] Removing metric index file: F:\parking\park-api\logs\sentinel\park-gateway-metrics.log.2025-07-31.2.idx
2025-08-01 13:20:21.234 INFO [MetricWriter] New metric file created: logs/sentinel\park-gateway-metrics.log.2025-08-01.3
2025-08-01 13:20:21.234 INFO [MetricWriter] New metric index file created: logs/sentinel\park-gateway-metrics.log.2025-08-01.3.idx
2025-08-01 13:20:36.282 INFO Add child <sentinel_gateway_context$$route$$park-auth> to node <machine-root>
2025-08-01 13:20:36.283 INFO Add child <park-auth> to node <sentinel_gateway_context$$route$$park-auth>
