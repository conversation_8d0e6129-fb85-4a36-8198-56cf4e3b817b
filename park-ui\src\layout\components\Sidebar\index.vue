<template>
  <div :class="{ 'has-logo': showLogo }" class="sidebar-container">
    <logo v-if="showLogo" :collapse="isCollapse" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        :default-active="activeMenu"
        :collapse="isCollapse"
        :background-color="getMenuBackground"
        :text-color="getMenuTextColor"
        :unique-opened="true"
        :active-text-color="theme"
        :collapse-transition="false"
        mode="vertical"
        :class="sideTheme"
        :router="false"
      >
        <sidebar-item
          v-for="(route, index) in sidebarRouters"
          :key="route.path + index"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script setup>
import Logo from './Logo'
import SidebarItem from './SidebarItem'
import variables from '@/assets/styles/variables.module.scss'
import useAppStore from '@/store/modules/app'
import useSettingsStore from '@/store/modules/settings'
import usePermissionStore from '@/store/modules/permission'

const route = useRoute()
const appStore = useAppStore()
const settingsStore = useSettingsStore()
const permissionStore = usePermissionStore()

const sidebarRouters = computed(() => permissionStore.sidebarRouters)
const showLogo = computed(() => settingsStore.sidebarLogo)
const sideTheme = computed(() => settingsStore.sideTheme)
const theme = computed(() => settingsStore.theme)
const isCollapse = computed(() => !appStore.sidebar.opened)

// 获取菜单背景色
const getMenuBackground = computed(() => {
  if (settingsStore.isDark) {
    return 'var(--sidebar-bg)'
  }
  return sideTheme.value === 'theme-dark' ? variables.menuBg : variables.menuLightBg
})

// 获取菜单文字颜色
const getMenuTextColor = computed(() => {
  if (settingsStore.isDark) {
    return 'var(--sidebar-text)'
  }
  return sideTheme.value === 'theme-dark' ? variables.menuText : variables.menuLightText
})

const activeMenu = computed(() => {
  const { meta, path } = route
  if (meta.activeMenu) {
    return meta.activeMenu
  }
  return path
})
</script>

<style lang="scss" scoped>
.sidebar-container {
  background-color: v-bind(getMenuBackground);

  .scrollbar-wrapper {
    background-color: v-bind(getMenuBackground);
  }

  .el-menu {
    border: none;
    height: 100%;
    width: 100% !important;

    .el-menu-item, .el-sub-menu__title {
      &:hover {
        background-color: var(--menu-hover, rgba(0, 0, 0, 0.06)) !important;
      }
    }

    .el-menu-item {
      color: v-bind(getMenuTextColor);

      &.is-active {
        color: var(--menu-active-text, #409eff);
        background-color: var(--menu-hover, rgba(0, 0, 0, 0.06)) !important;
      }
    }

    .el-sub-menu__title {
      color: v-bind(getMenuTextColor);
    }
  }
}

/* 星空主题侧边栏样式 */
html.dark .sidebar-container {
  background: rgba(15, 20, 25, 0.9) !important;
  backdrop-filter: blur(15px);
  border-right: 1px solid rgba(100, 200, 255, 0.2) !important;
  position: relative;

  // 添加微妙的星空效果
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-image:
      radial-gradient(1px 1px at 20px 30px, rgba(255,255,255,0.3), transparent),
      radial-gradient(1px 1px at 40px 70px, rgba(100,200,255,0.2), transparent),
      radial-gradient(1px 1px at 90px 40px, rgba(255,255,255,0.2), transparent);
    background-repeat: repeat;
    background-size: 120px 80px;
    animation: starry-twinkle 6s ease-in-out infinite alternate;
    pointer-events: none;
    z-index: 1;
  }

  .scrollbar-wrapper {
    background: transparent !important;
    position: relative;
    z-index: 2;
  }

  .el-menu {
    background: transparent !important;

    .el-menu-item {
      background: transparent !important;
      color: #f8f8ff !important; /* 月光白 */
      text-shadow: 0 0 8px rgba(248, 248, 255, 0.6);
      transition: all 0.3s ease;
      position: relative;
      margin: 2px 8px;
      border-radius: 8px;

      &:hover {
        background: rgba(248, 248, 255, 0.1) !important;
        color: #ffffff !important;
        text-shadow: 0 0 12px rgba(248, 248, 255, 0.8);
        transform: translateX(4px);
        box-shadow: 0 4px 12px rgba(248, 248, 255, 0.2);
      }

      &.is-active {
        background: rgba(248, 248, 255, 0.15) !important; /* 月光白背景 */
        color: #f8f8ff !important; /* 月光白文字 */
        text-shadow: 0 0 15px rgba(248, 248, 255, 0.9);
        border-right: 3px solid #f8f8ff; /* 月光白边框 */
        box-shadow:
          0 4px 12px rgba(248, 248, 255, 0.3),
          inset 0 1px 0 rgba(248, 248, 255, 0.2);

        &::before {
          content: '';
          position: absolute;
          left: 0;
          top: 0;
          bottom: 0;
          width: 4px;
          background: linear-gradient(to bottom, #f8f8ff, #e6e6fa); /* 月光白渐变 */
          border-radius: 0 4px 4px 0;
          box-shadow: 0 0 8px rgba(248, 248, 255, 0.8);
        }
      }

      .el-icon {
        color: #f8f8ff !important; /* 月光白图标 */
        filter: drop-shadow(0 0 6px rgba(248, 248, 255, 0.6));
        transition: all 0.3s ease;
      }

      &:hover .el-icon {
        color: #ffffff !important;
        filter: drop-shadow(0 0 10px rgba(248, 248, 255, 0.8));
        transform: scale(1.1);
      }

      &.is-active .el-icon {
        color: #f8f8ff !important; /* 选中状态月光白图标 */
        filter: drop-shadow(0 0 12px rgba(248, 248, 255, 0.9));
      }
    }

    .el-sub-menu {
      .el-sub-menu__title {
        background: transparent !important;
        color: #f8f8ff !important; /* 月光白 */
        text-shadow: 0 0 8px rgba(248, 248, 255, 0.6);
        transition: all 0.3s ease;
        margin: 2px 8px;
        border-radius: 8px;

        &:hover {
          background: rgba(248, 248, 255, 0.1) !important;
          color: #ffffff !important;
          text-shadow: 0 0 12px rgba(248, 248, 255, 0.8);
          transform: translateX(4px);
          box-shadow: 0 4px 12px rgba(248, 248, 255, 0.2);
        }

        .el-icon {
          color: #f8f8ff !important; /* 月光白图标 */
          filter: drop-shadow(0 0 6px rgba(248, 248, 255, 0.6));
          transition: all 0.3s ease;
        }

        &:hover .el-icon {
          color: #ffffff !important;
          filter: drop-shadow(0 0 10px rgba(248, 248, 255, 0.8));
          transform: scale(1.1);
        }
      }

      .el-menu {
        background: rgba(15, 20, 25, 0.5) !important;
        margin: 4px 8px;
        border-radius: 8px;
        border: 1px solid rgba(100, 200, 255, 0.1);
      }
    }
  }
}
</style>
