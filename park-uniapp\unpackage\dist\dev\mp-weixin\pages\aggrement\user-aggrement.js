"use strict";
const common_vendor = require("../../common/vendor.js");
const api_agreement = require("../../api/agreement.js");
const _sfc_main = {
  __name: "user-aggrement",
  setup(__props) {
    const loading = common_vendor.ref(true);
    const error = common_vendor.ref("");
    const agreementData = common_vendor.ref({});
    const loadAgreement = async () => {
      try {
        loading.value = true;
        error.value = "";
        const response = await api_agreement.getAgreementByType(0);
        if (response.code === 200) {
          agreementData.value = response.data || {};
        } else {
          error.value = response.msg || "获取协议失败";
        }
      } catch (err) {
        common_vendor.index.__f__("error", "at pages/aggrement/user-aggrement.vue:59", "加载用户协议失败:", err);
        error.value = "网络错误，请稍后重试";
      } finally {
        loading.value = false;
      }
    };
    const goBack = () => {
      common_vendor.index.navigateBack();
    };
    common_vendor.onLoad(() => {
      loadAgreement();
    });
    return (_ctx, _cache) => {
      return common_vendor.e({
        a: common_vendor.o(goBack),
        b: loading.value
      }, loading.value ? {} : error.value ? {
        d: common_vendor.t(error.value),
        e: common_vendor.o(loadAgreement)
      } : {
        f: agreementData.value.agreementContent || "暂无内容"
      }, {
        c: error.value,
        g: common_vendor.gei(_ctx, "")
      });
    };
  }
};
const MiniProgramPage = /* @__PURE__ */ common_vendor._export_sfc(_sfc_main, [["__scopeId", "data-v-66212410"]]);
wx.createPage(MiniProgramPage);
//# sourceMappingURL=../../../.sourcemap/mp-weixin/pages/aggrement/user-aggrement.js.map
